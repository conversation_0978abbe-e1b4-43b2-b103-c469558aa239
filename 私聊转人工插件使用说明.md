# 私聊转人工插件使用说明

## 功能概述

私聊转人工插件是一个智能的客服转接系统，当用户在私聊中发送特定关键词时，系统会自动：

1. **关键词触发**：检测转人工关键词（如"转人工"、"投诉"、"退款"等）
2. **AI总结上下文**：分析用户的对话历史，生成智能总结
3. **自动打标签**：根据对话内容为用户生成标签（问题类型、情感倾向、用户活跃度等）
4. **导出报告**：生成详细的转人工报告文件
5. **自动加黑名单**：将用户自动加入黑名单，避免继续自动回复

## 插件特性

- ✅ 智能关键词检测
- ✅ AI上下文分析和总结
- ✅ 自动用户标签生成
- ✅ 详细转人工报告导出
- ✅ 自动黑名单管理
- ✅ 处理状态跟踪
- ✅ 用户档案管理
- ✅ 统计分析功能

## 安装和配置

### 1. 插件自动加载

插件会在系统启动时自动加载，优先级为10（高优先级，仅次于黑名单插件）。

### 2. 数据库表自动创建

插件首次运行时会自动创建以下数据库表：
- `handover_keywords`：转人工触发关键词表
- `user_tags`：用户标签表
- `handover_records`：转人工记录表

### 3. 默认关键词

系统会自动添加以下默认触发关键词：
- 转人工
- 人工客服
- 联系客服
- 投诉
- 退款
- 问题反馈
- 人工服务
- 客服
- 转接
- 帮助

## 工作流程

### 1. 触发检测
当用户在私聊中发送包含触发关键词的消息时，插件会：
- 立即回复转人工提示消息
- 启动后台处理流程

### 2. 上下文分析
系统会收集用户的对话历史（最近72小时内的消息），并进行智能分析：
- 分析问题类型（投诉、退款、咨询等）
- 检测情感倾向（负面、中性等）
- 评估紧急程度
- 分析对话复杂度

### 3. 用户标签生成
基于对话内容自动生成标签：
- **问题类型**：投诉、退款、技术故障、咨询、建议反馈
- **用户活跃度**：新用户、普通用户、高频用户
- **咨询时间**：工作时间、非工作时间
- **情感倾向**：负面情绪、轻微不满、中性

### 4. 报告导出
生成详细的转人工报告文件，包含：
- 基本信息（用户、时间、触发关键词等）
- AI上下文总结
- 用户标签
- 对话历史
- 处理建议
- 状态信息

### 5. 自动加黑名单
将用户自动加入私聊黑名单，避免机器人继续自动回复。

## 使用方法

### 1. 管理工具使用

系统提供了 `human_handover_manager.py` 管理工具：

#### 查看触发关键词
```bash
python human_handover_manager.py list_keywords
```

#### 添加/删除关键词
```bash
python human_handover_manager.py add_keyword "技术支持"
python human_handover_manager.py remove_keyword "帮助"
```

#### 查看转人工记录
```bash
python human_handover_manager.py list_records           # 所有记录
python human_handover_manager.py list_records pending   # 待处理记录
python human_handover_manager.py list_records handled   # 已处理记录
```

#### 查看用户档案
```bash
python human_handover_manager.py user_profile "张三"
```

#### 根据标签搜索用户
```bash
python human_handover_manager.py search_tag "问题类型"
python human_handover_manager.py search_tag "问题类型" "投诉"
```

#### 更新处理状态
```bash
python human_handover_manager.py update_status 1 handled "已联系用户处理"
```

#### 导出记录
```bash
python human_handover_manager.py export              # 导出所有记录
python human_handover_manager.py export pending      # 导出待处理记录
```

#### 查看统计信息
```bash
python human_handover_manager.py statistics
```

### 2. 编程接口

也可以通过代码直接调用插件接口：

```python
# 获取插件实例
handover_plugin = None
for plugin in handler.plugins:
    if plugin.__class__.__name__ == 'HumanHandoverPlugin':
        handover_plugin = plugin
        break

if handover_plugin:
    # 添加触发关键词
    handover_plugin.add_trigger_keyword("技术支持", priority=5)
    
    # 获取用户档案
    profile = handover_plugin.get_user_profile("张三")
    
    # 搜索用户
    users = handover_plugin.search_users_by_tag("问题类型", "投诉")
    
    # 获取统计信息
    stats = handover_plugin.get_handover_statistics()
```

## 报告文件说明

转人工报告文件保存在 `handover_reports` 目录下，文件名格式：
`handover_{用户名}_{时间戳}.txt`

报告内容包括：

### 基本信息
- 记录ID
- 用户名称
- 触发关键词
- 触发时间
- 触发消息

### AI上下文总结
基于对话历史的智能分析，包括：
- 问题类型识别
- 情感倾向分析
- 紧急程度评估
- 对话复杂度分析

### 用户标签
自动生成的用户标签，包含：
- 标签名称
- 标签值
- 置信度

### 对话历史
用户最近的对话记录（最多20条）

### 处理建议
基于分析结果生成的处理建议：
- 优先级建议
- 处理时间建议
- 部门转接建议
- 特殊注意事项

## 数据库结构

### handover_keywords 表
存储触发关键词配置

### user_tags 表
存储用户标签信息，支持：
- 自动生成标签
- 手动添加标签
- 标签置信度
- 标签类型区分

### handover_records 表
存储转人工记录，包含：
- 完整的转人工信息
- JSON格式的标签快照
- JSON格式的对话历史快照
- 处理状态跟踪

## 监控和维护

### 查看插件状态
插件启动时会在日志中显示：
```
✅ 转人工相关表初始化完成
📋 加载了 10 个转人工触发关键词
✅ 私聊转人工插件已启动
```

### 查看处理日志
每次转人工时会记录详细日志：
```
🚨 检测到转人工关键词 '投诉' 来自用户 '张三'
🔄 开始处理用户 '张三' 的转人工请求
📊 为用户 '张三' 生成了 4 个标签
📄 转人工报告已导出: handover_reports/handover_张三_20241225_143022.txt
🚫 用户 '张三' 已自动加入黑名单
✅ 用户 '张三' 转人工处理完成，记录ID: 123
```

## 注意事项

1. **只处理私聊**：插件只处理私聊消息，不处理群聊
2. **自动加黑名单**：转人工后用户会自动加入黑名单
3. **报告文件管理**：定期清理旧的报告文件
4. **数据库维护**：定期清理历史记录和过期标签
5. **关键词管理**：根据实际需求调整触发关键词

## 扩展功能

插件设计为可扩展的，可以根据需要添加：
- 更智能的AI分析（接入GPT等）
- 更复杂的标签规则
- 邮件/短信通知功能
- 工单系统集成
- 客服分配算法
- 满意度调查功能

## 故障排除

### 常见问题

1. **关键词不触发**
   - 检查关键词是否正确添加
   - 确认插件已正常启动
   - 查看日志输出

2. **报告文件未生成**
   - 检查 `handover_reports` 目录权限
   - 查看错误日志

3. **标签生成异常**
   - 检查对话记录插件是否正常
   - 查看数据库连接状态

4. **黑名单添加失败**
   - 确认黑名单插件已加载
   - 检查数据库权限

### 调试方法

1. 查看数据库表内容
2. 检查插件日志输出
3. 使用管理工具查看状态
4. 检查报告文件内容
