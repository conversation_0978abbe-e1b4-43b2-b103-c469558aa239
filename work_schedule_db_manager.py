#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工作时间数据库管理工具
用于管理存储在数据库中的人工服务工作时间配置
"""

import sys
import json
from datetime import datetime
from utils.config_utils import load_config
from db_plugins.mysql import MySQLDB

class WorkScheduleDBManager:
    """工作时间数据库管理器"""
    
    def __init__(self):
        # 加载数据库配置
        config = load_config()
        db_config = config.get("mysql", {})
        if not db_config:
            raise ValueError("❌ config.json 中未配置 mysql 数据库信息")
        
        # 连接数据库
        self.db = MySQLDB(**db_config)
        self.db.connect()
        
        # 注册SQL函数
        from plugins.human_handover.sql import HUMAN_HANDOVER_SQL_FUNCTIONS
        self.db.register_plugin_functions("WorkScheduleDBManager", HUMAN_HANDOVER_SQL_FUNCTIONS)
        
        # 初始化表
        try:
            self.db.init_human_handover_tables()
            print("✅ 数据库表初始化完成")
        except Exception as e:
            print(f"❌ 初始化数据库表失败: {e}")
    
    def show_current_config(self):
        """显示当前工作时间配置"""
        try:
            config = self.db.get_work_schedule_config()
            
            print("📅 当前工作时间配置（数据库）")
            print("=" * 50)
            
            print(f"🔧 基本设置:")
            print(f"   - 工作时间功能: {'启用' if config.get('enabled', True) else '禁用'}")
            print(f"   - 工作日: {config.get('work_days', [1, 2, 3, 4, 5])}")
            print(f"   - 工作时间: {config.get('work_start', '09:00')} - {config.get('work_end', '18:00')}")
            print(f"   - 时区: {config.get('timezone', 'Asia/Shanghai')}")
            print(f"   - 节假日模式: {'是' if config.get('holiday_mode', False) else '否'}")
            
            print(f"\n💬 响应消息:")
            print(f"   - 工作时间: {config.get('work_hours_response', '默认消息')}")
            print(f"   - 非工作时间: {config.get('non_work_hours_response', '默认消息')}")
            print(f"   - 周末时间: {config.get('weekend_response', '默认消息')}")
            print(f"   - 节假日: {config.get('holiday_response', '默认消息')}")
            
            # 显示当前状态
            now = datetime.now()
            weekday = now.isoweekday()
            current_time = now.time()
            
            print(f"\n🕐 当前状态:")
            print(f"   - 当前时间: {now.strftime('%Y-%m-%d %H:%M:%S')} (周{weekday})")
            
            # 简单判断当前是否为工作时间
            work_days = config.get('work_days', [1, 2, 3, 4, 5])
            work_start = config.get('work_start', '09:00')
            work_end = config.get('work_end', '18:00')
            holiday_mode = config.get('holiday_mode', False)
            
            if holiday_mode:
                status = "节假日时间"
            elif weekday not in work_days:
                status = "周末时间"
            else:
                start_hour, start_minute = map(int, work_start.split(':'))
                end_hour, end_minute = map(int, work_end.split(':'))
                if start_hour <= current_time.hour <= end_hour:
                    if current_time.hour == end_hour and current_time.minute > end_minute:
                        status = "非工作时间"
                    elif current_time.hour == start_hour and current_time.minute < start_minute:
                        status = "非工作时间"
                    else:
                        status = "工作时间"
                else:
                    status = "非工作时间"
            
            print(f"   - 当前状态: {status}")
            
        except Exception as e:
            print(f"❌ 获取配置失败: {e}")
    
    def set_work_hours(self, start_time: str, end_time: str):
        """设置工作时间"""
        try:
            # 验证时间格式
            start_hour, start_minute = map(int, start_time.split(':'))
            end_hour, end_minute = map(int, end_time.split(':'))
            
            if not (0 <= start_hour <= 23 and 0 <= start_minute <= 59):
                print("❌ 开始时间格式错误")
                return
            
            if not (0 <= end_hour <= 23 and 0 <= end_minute <= 59):
                print("❌ 结束时间格式错误")
                return
            
            # 更新数据库配置
            self.db.set_work_schedule_config('work_start', start_time, 'string', '工作开始时间')
            self.db.set_work_schedule_config('work_end', end_time, 'string', '工作结束时间')
            
            print(f"✅ 工作时间已更新: {start_time} - {end_time}")
            print("⚠️ 请重启机器人程序使配置生效")
            
        except Exception as e:
            print(f"❌ 设置工作时间失败: {e}")
    
    def set_work_days(self, days: str):
        """设置工作日"""
        try:
            # 解析工作日
            if days.lower() == 'weekdays':
                work_days = [1, 2, 3, 4, 5]  # 周一到周五
            elif days.lower() == 'all':
                work_days = [1, 2, 3, 4, 5, 6, 7]  # 全周
            else:
                work_days = [int(d.strip()) for d in days.split(',')]
                # 验证工作日
                for day in work_days:
                    if not (1 <= day <= 7):
                        print("❌ 工作日必须在1-7之间（1=周一，7=周日）")
                        return
            
            # 更新数据库配置
            work_days_json = json.dumps(work_days)
            self.db.set_work_schedule_config('work_days', work_days_json, 'json', '工作日设置（1=周一，7=周日）')
            
            day_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
            work_day_names = [day_names[d-1] for d in work_days]
            print(f"✅ 工作日已更新: {', '.join(work_day_names)}")
            print("⚠️ 请重启机器人程序使配置生效")
            
        except Exception as e:
            print(f"❌ 设置工作日失败: {e}")
    
    def toggle_holiday_mode(self, enable: bool = None):
        """切换节假日模式"""
        try:
            if enable is None:
                # 获取当前状态并切换
                current_config = self.db.get_work_schedule_config('holiday_mode')
                if current_config:
                    current_mode = current_config['parsed_value']
                else:
                    current_mode = False
                new_mode = not current_mode
            else:
                new_mode = enable
            
            # 更新数据库配置
            self.db.set_work_schedule_config('holiday_mode', 'true' if new_mode else 'false', 'boolean', '节假日模式')
            
            print(f"✅ 节假日模式已{'启用' if new_mode else '禁用'}")
            print("⚠️ 请重启机器人程序使配置生效")
            
        except Exception as e:
            print(f"❌ 切换节假日模式失败: {e}")
    
    def set_response_message(self, time_type: str, message: str):
        """设置响应消息"""
        try:
            valid_types = {
                'work_hours': ('work_hours_response', '工作时间响应消息'),
                'non_work_hours': ('non_work_hours_response', '非工作时间响应消息'),
                'weekend': ('weekend_response', '周末时间响应消息'),
                'holiday': ('holiday_response', '节假日响应消息')
            }
            
            if time_type not in valid_types:
                print(f"❌ 时间类型必须是: {', '.join(valid_types.keys())}")
                return
            
            config_key, description = valid_types[time_type]
            
            # 更新数据库配置
            self.db.set_work_schedule_config(config_key, message, 'string', description)
            
            type_names = {
                'work_hours': '工作时间',
                'non_work_hours': '非工作时间',
                'weekend': '周末时间',
                'holiday': '节假日'
            }
            
            print(f"✅ {type_names[time_type]}响应消息已更新")
            print("⚠️ 请重启机器人程序使配置生效")
            
        except Exception as e:
            print(f"❌ 设置响应消息失败: {e}")
    
    def enable_work_schedule(self, enable: bool = True):
        """启用/禁用工作时间功能"""
        try:
            self.db.set_work_schedule_config('enabled', 'true' if enable else 'false', 'boolean', '是否启用工作时间功能')
            
            print(f"✅ 工作时间功能已{'启用' if enable else '禁用'}")
            print("⚠️ 请重启机器人程序使配置生效")
            
        except Exception as e:
            print(f"❌ 设置工作时间功能失败: {e}")
    
    def list_all_configs(self):
        """列出所有配置项"""
        try:
            cursor = self.db.connection.cursor(dictionary=True)
            cursor.execute("""
                SELECT config_key, config_value, config_type, description, updated_at
                FROM work_schedule_config
                ORDER BY config_key
            """)
            results = cursor.fetchall()
            
            if not results:
                print("📋 暂无工作时间配置")
                return
            
            print("📋 所有工作时间配置项:")
            print("=" * 80)
            
            for row in results:
                print(f"配置项: {row['config_key']}")
                print(f"   值: {row['config_value']}")
                print(f"   类型: {row['config_type']}")
                print(f"   描述: {row['description']}")
                print(f"   更新时间: {row['updated_at']}")
                print("-" * 40)
                
        except Exception as e:
            print(f"❌ 获取配置列表失败: {e}")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("工作时间数据库管理工具")
        print("=" * 50)
        print("用法:")
        print("  python work_schedule_db_manager.py show                        # 显示当前配置")
        print("  python work_schedule_db_manager.py set_hours <开始> <结束>     # 设置工作时间")
        print("  python work_schedule_db_manager.py set_days <工作日>           # 设置工作日")
        print("  python work_schedule_db_manager.py holiday_on                  # 启用节假日模式")
        print("  python work_schedule_db_manager.py holiday_off                 # 禁用节假日模式")
        print("  python work_schedule_db_manager.py set_message <类型> <消息>   # 设置响应消息")
        print("  python work_schedule_db_manager.py enable                      # 启用工作时间功能")
        print("  python work_schedule_db_manager.py disable                     # 禁用工作时间功能")
        print("  python work_schedule_db_manager.py list                        # 列出所有配置")
        print()
        print("示例:")
        print("  python work_schedule_db_manager.py set_hours 09:00 18:00")
        print("  python work_schedule_db_manager.py set_days weekdays           # 周一到周五")
        print("  python work_schedule_db_manager.py set_days 1,2,3,4,5,6        # 周一到周六")
        print("  python work_schedule_db_manager.py set_message work_hours '工作时间回复'")
        return
    
    try:
        manager = WorkScheduleDBManager()
        command = sys.argv[1].lower()
        
        if command == 'show':
            manager.show_current_config()
            
        elif command == 'set_hours':
            if len(sys.argv) < 4:
                print("❌ 请提供开始时间和结束时间")
                print("示例: python work_schedule_db_manager.py set_hours 09:00 18:00")
                return
            start_time = sys.argv[2]
            end_time = sys.argv[3]
            manager.set_work_hours(start_time, end_time)
            
        elif command == 'set_days':
            if len(sys.argv) < 3:
                print("❌ 请提供工作日")
                print("示例: python work_schedule_db_manager.py set_days weekdays")
                print("      python work_schedule_db_manager.py set_days 1,2,3,4,5")
                return
            days = sys.argv[2]
            manager.set_work_days(days)
            
        elif command == 'holiday_on':
            manager.toggle_holiday_mode(True)
            
        elif command == 'holiday_off':
            manager.toggle_holiday_mode(False)
            
        elif command == 'set_message':
            if len(sys.argv) < 4:
                print("❌ 请提供时间类型和消息内容")
                print("时间类型: work_hours, non_work_hours, weekend, holiday")
                return
            time_type = sys.argv[2]
            message = sys.argv[3]
            manager.set_response_message(time_type, message)
            
        elif command == 'enable':
            manager.enable_work_schedule(True)
            
        elif command == 'disable':
            manager.enable_work_schedule(False)
            
        elif command == 'list':
            manager.list_all_configs()
            
        else:
            print(f"❌ 未知命令: {command}")
            
    except Exception as e:
        print(f"❌ 执行失败: {e}")

if __name__ == "__main__":
    main()
