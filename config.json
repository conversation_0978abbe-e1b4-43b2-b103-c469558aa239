{"mode": "batch", "bot_name": "春", "mysql": {"host": "***********", "user": "yunju", "password": "nmnm1239", "database": "wechat_bot"}, "enable_at_reply": true, "enable_private_ai": true, "user_data_collection": {"force_collection_on_startup": false, "collection_interval_hours": 24, "description": "用户数据采集配置 - force_collection_on_startup: 是否强制在启动时采集数据; collection_interval_hours: 数据采集间隔（小时）"}, "deepseek": {"api_key": "***********************************", "description": "DeepSeek API配置 - 用于转人工插件的AI分析功能"}, "message_segmentation": {"enabled": true, "mode": "force", "max_length": 100, "probability": 0.3, "segment_delay": 1.0, "description": "消息分段回复配置 - enabled: 是否启用分段功能; mode: 分段模式(none/force/auto/probability); max_length: 触发分段的字数阈值; probability: 概率分段模式下的分段概率(0-1); segment_delay: 分段消息间隔时间(秒)"}, "human_handover": {"enabled": true, "work_schedule": {"enabled": true, "work_days": [1, 2, 3, 4, 5], "work_hours": {"start": "09:00", "end": "18:00"}, "timezone": "Asia/Shanghai", "holiday_mode": false}, "responses": {"work_hours": "您好！我已为您转接人工客服，客服人员会尽快为您处理。", "non_work_hours": "您好！现在是非工作时间（工作时间：周一至周五 09:00-18:00），您的问题已记录，客服人员会在工作时间内优先为您处理。如有紧急问题，请留下详细描述。", "weekend": "您好！现在是周末时间，您的问题已记录，客服人员会在下个工作日优先为您处理。如有紧急问题，请留下详细描述。", "holiday": "您好！现在是节假日期间，您的问题已记录，客服人员会在工作日恢复后优先为您处理。感谢您的理解！"}, "description": "人工服务配置 - work_schedule: 工作时间安排; responses: 不同时间段的回复消息"}}