{"mode": "batch", "bot_name": "春", "mysql": {"host": "***********", "user": "yunju", "password": "nmnm1239", "database": "wechat_bot"}, "enable_at_reply": true, "enable_private_ai": true, "user_data_collection": {"force_collection_on_startup": false, "collection_interval_hours": 24, "description": "用户数据采集配置 - force_collection_on_startup: 是否强制在启动时采集数据; collection_interval_hours: 数据采集间隔（小时）"}, "deepseek": {"api_key": "***********************************", "description": "DeepSeek API配置 - 用于转人工插件的AI分析功能"}, "message_segmentation": {"enabled": true, "mode": "force", "max_length": 100, "probability": 0.3, "segment_delay": 1.0, "description": "消息分段回复配置 - enabled: 是否启用分段功能; mode: 分段模式(none/force/auto/probability); max_length: 触发分段的字数阈值; probability: 概率分段模式下的分段概率(0-1); segment_delay: 分段消息间隔时间(秒)"}}