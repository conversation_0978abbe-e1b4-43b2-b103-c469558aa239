import json
import requests
from typing import Dict, Any

from models import AIModel
# 简单助手

class DifyAgentModel(AIModel):
    def generate(self, context: str, prompt_template: str = None) -> str:
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        payload = {
            "inputs": {},  # 可以根据需要传入变量
            "query": context,
            "response_mode": "streaming",
            "conversation_id": "",  # 留空表示新对话
            "user": "terminal_user",  # 固定标识
            "files": []  # 默认不上传文件
        }

        final_answer = ""
        try:
            with requests.post(self.url, headers=headers, json=payload, stream=True, timeout=60) as response:
                if response.status_code == 200:
                    for line in response.iter_lines():
                        if line:
                            decoded_line = line.decode('utf-8')
                            if decoded_line.startswith("data:"):
                                data_part = decoded_line[5:].strip()
                                try:
                                    json_data = json.loads(data_part)
                                    event = json_data.get("event")

                                    # 处理流式回答块
                                    if event == "message":
                                        answer_chunk = json_data.get("answer", "")
                                        final_answer += answer_chunk

                                    # 遇到结束事件，退出循环
                                    elif event == "message_end":
                                        break

                                except json.JSONDecodeError:
                                    continue  # 忽略无效行
                else:
                    return f"\n请求失败，状态码：{response.status_code}"
        except Exception as e:
            return f"\n发生错误：{e}"

        return final_answer.strip() or "\n未获得有效回复。"