#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试工作时间数据库配置功能
"""

import sys
import os
import json

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

def test_work_schedule_db():
    """测试工作时间数据库配置功能"""
    print("🗄️ 测试工作时间数据库配置功能")
    print("=" * 60)
    
    # 创建模拟数据库
    class MockDB:
        def __init__(self):
            # 模拟数据库中的配置数据
            self.config_data = {
                'enabled': True,
                'work_days': [1, 2, 3, 4, 5],
                'work_start': '09:00',
                'work_end': '18:00',
                'timezone': 'Asia/Shanghai',
                'holiday_mode': False,
                'work_hours_response': '您好！我已为您转接人工客服，客服人员会尽快为您处理。',
                'non_work_hours_response': '您好！现在是非工作时间（工作时间：周一至周五 09:00-18:00），您的问题已记录，客服人员会在工作时间内优先为您处理。如有紧急问题，请留下详细描述。',
                'weekend_response': '您好！现在是周末时间，您的问题已记录，客服人员会在下个工作日优先为您处理。如有紧急问题，请留下详细描述。',
                'holiday_response': '您好！现在是节假日期间，您的问题已记录，客服人员会在工作日恢复后优先为您处理。感谢您的理解！'
            }
        
        def register_plugin_functions(self, name, functions):
            pass
        
        def init_human_handover_tables(self):
            print("✅ 模拟数据库表初始化完成")
        
        def get_handover_keywords(self):
            return [
                {'keyword': '转人工', 'priority': 1},
                {'keyword': '人工客服', 'priority': 2},
                {'keyword': '投诉', 'priority': 3}
            ]
        
        def get_work_schedule_config(self, config_key=None):
            """模拟获取工作时间配置"""
            if config_key:
                return {
                    'config_key': config_key,
                    'parsed_value': self.config_data.get(config_key)
                }
            else:
                return self.config_data
        
        def set_work_schedule_config(self, config_key, config_value, config_type='string', description=None):
            """模拟设置工作时间配置"""
            # 解析值
            if config_type == 'boolean':
                parsed_value = config_value.lower() in ('true', '1', 'yes', 'on')
            elif config_type == 'integer':
                parsed_value = int(config_value)
            elif config_type == 'json':
                parsed_value = json.loads(config_value)
            else:  # string
                parsed_value = config_value
            
            self.config_data[config_key] = parsed_value
            print(f"✅ 模拟设置配置: {config_key} = {parsed_value}")
            return True
    
    # 创建模拟handler
    class MockHandler:
        def __init__(self):
            self.db = MockDB()
        
        def _log(self, message, level='INFO'):
            print(f"[{level}] {message}")
    
    # 测试插件加载
    print("🔧 测试插件加载...")
    try:
        from plugins.human_handover.plugin import HumanHandoverPlugin
        
        handler = MockHandler()
        plugin = HumanHandoverPlugin(handler)
        
        print(f"✅ 插件加载成功")
        print(f"📋 配置信息:")
        print(f"   - 工作时间功能: {'启用' if plugin.work_schedule_enabled else '禁用'}")
        print(f"   - 工作日: {plugin.work_days}")
        print(f"   - 工作时间: {plugin.work_start} - {plugin.work_end}")
        print(f"   - 节假日模式: {'是' if plugin.holiday_mode else '否'}")
        
    except Exception as e:
        print(f"❌ 插件加载失败: {e}")
        import traceback
        print(traceback.format_exc())
        return
    
    # 测试配置修改
    print(f"\n🔧 测试配置修改...")
    
    # 测试修改工作时间
    print("测试修改工作时间...")
    handler.db.set_work_schedule_config('work_start', '08:30', 'string', '工作开始时间')
    handler.db.set_work_schedule_config('work_end', '17:30', 'string', '工作结束时间')
    
    # 测试修改工作日
    print("测试修改工作日...")
    handler.db.set_work_schedule_config('work_days', '[1,2,3,4,5,6]', 'json', '工作日设置')
    
    # 测试修改节假日模式
    print("测试修改节假日模式...")
    handler.db.set_work_schedule_config('holiday_mode', 'true', 'boolean', '节假日模式')
    
    # 测试修改响应消息
    print("测试修改响应消息...")
    handler.db.set_work_schedule_config('work_hours_response', '客服在线，马上为您处理！', 'string', '工作时间响应消息')
    
    # 重新加载配置
    print(f"\n🔄 重新加载配置...")
    plugin._load_work_schedule_config()
    
    print(f"📋 更新后的配置:")
    print(f"   - 工作时间功能: {'启用' if plugin.work_schedule_enabled else '禁用'}")
    print(f"   - 工作日: {plugin.work_days}")
    print(f"   - 工作时间: {plugin.work_start} - {plugin.work_end}")
    print(f"   - 节假日模式: {'是' if plugin.holiday_mode else '否'}")
    print(f"   - 工作时间响应: {plugin.work_hours_response}")
    
    # 测试时间判断
    print(f"\n🕐 测试时间判断...")
    is_work_time, time_type = plugin._is_work_time()
    print(f"   - 当前是否工作时间: {'是' if is_work_time else '否'}")
    print(f"   - 时间类型: {time_type}")
    
    # 测试响应消息
    print(f"\n💬 测试响应消息...")
    response = plugin._get_handover_response("转人工")
    print(f"   - 响应消息: {response}")
    
    # 测试不同时间场景
    print(f"\n📅 测试不同时间场景...")
    
    # 模拟节假日模式
    plugin.holiday_mode = True
    is_work_time, time_type = plugin._is_work_time()
    response = plugin._get_handover_response("转人工")
    print(f"   节假日模式: {time_type} - {response[:50]}...")
    
    # 恢复正常模式
    plugin.holiday_mode = False
    
    print(f"\n🎉 数据库配置功能测试完成！")
    print("✅ 所有功能正常工作")
    print("✅ 配置可以从数据库正确加载和保存")
    print("✅ 时间判断逻辑正常")
    print("✅ 响应消息根据时间正确返回")

if __name__ == "__main__":
    test_work_schedule_db()
