/*
 Navicat Premium Data Transfer

 Source Server         : mysql
 Source Server Type    : MySQL
 Source Server Version : 80027
 Source Host           : localhost:3306
 Source Schema         : wechat_bot

 Target Server Type    : MySQL
 Target Server Version : 80027
 File Encoding         : 65001

 Date: 23/05/2025 11:02:00
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for ai_keyword_rules_collection
-- ----------------------------
DROP TABLE IF EXISTS `ai_keyword_rules_collection`;
CREATE TABLE `ai_keyword_rules_collection`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `keyword_id` bigint NOT NULL,
  `user_keyword` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `agent_id` bigint NOT NULL,
  `reply_prompt` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `fk_keyword_id`(`keyword_id` ASC) USING BTREE,
  INDEX `fk_agent_id`(`agent_id` ASC) USING BTREE,
  INDEX `idx_user_keyword`(`user_keyword` ASC) USING BTREE,
  CONSTRAINT `ai_keyword_rules_collection_ibfk_1` FOREIGN KEY (`keyword_id`) REFERENCES `ai_keyword_rules` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `ai_keyword_rules_collection_ibfk_2` FOREIGN KEY (`agent_id`) REFERENCES `ai_agent_profiles` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
