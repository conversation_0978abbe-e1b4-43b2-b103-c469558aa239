import mysql.connector
from mysql.connector import Error
from typing import List, Dict, Callable


class MySQLDB:
    def __init__(self, host: str, user: str, password: str, database: str):
        self.host = host
        self.user = user
        self.password = password
        self.database = database
        self.connection = None
        # 用于存储插件注册的SQL函数
        self._plugin_functions = {}  # {plugin_name: {function_name: function}}

    def connect(self):
        """连接到 MySQL 数据库"""
        try:
            self.connection = mysql.connector.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database
            )
            if self.connection.is_connected():
                print("✅ 成功连接到 MySQL 数据库")
        except Error as e:
            raise Exception(f"❌ 连接数据库失败: {e}")

    def register_plugin_functions(self, plugin_name: str, functions: Dict[str, Callable]):
        """
        注册插件的SQL函数
        :param plugin_name: 插件名称
        :param functions: 函数字典 {function_name: function}
        """
        if plugin_name not in self._plugin_functions:
            self._plugin_functions[plugin_name] = {}

        for func_name, func in functions.items():
            # 将函数绑定到当前实例
            bound_func = func.__get__(self, self.__class__)
            self._plugin_functions[plugin_name][func_name] = bound_func
            # 动态添加到类实例
            setattr(self, func_name, bound_func)
            print(f"✅ 注册插件 {plugin_name} 的SQL函数: {func_name}")

    def unregister_plugin_functions(self, plugin_name: str):
        """
        卸载插件的SQL函数
        :param plugin_name: 插件名称
        """
        if plugin_name in self._plugin_functions:
            for func_name in self._plugin_functions[plugin_name]:
                # 从实例中移除函数
                if hasattr(self, func_name):
                    delattr(self, func_name)
                    print(f"🗑️ 卸载插件 {plugin_name} 的SQL函数: {func_name}")

            del self._plugin_functions[plugin_name]
            print(f"✅ 插件 {plugin_name} 的所有SQL函数已卸载")

    def get_registered_functions(self) -> Dict[str, List[str]]:
        """
        获取所有已注册的插件SQL函数
        :return: {plugin_name: [function_names]}
        """
        return {
            plugin_name: list(functions.keys())
            for plugin_name, functions in self._plugin_functions.items()
        }

    def close(self):
        """关闭数据库连接"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            print("🔌 数据库连接已关闭")