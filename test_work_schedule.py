#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试人工服务工作时间功能
"""

import sys
import os
from datetime import datetime, time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

def test_work_schedule():
    """测试工作时间判断功能"""
    print("🕒 测试人工服务工作时间功能")
    print("=" * 60)

    # 创建模拟handler和数据库
    class MockDB:
        def __init__(self):
            # 模拟工作时间配置数据
            self.work_config = {
                'enabled': True,
                'work_days': [1, 2, 3, 4, 5],
                'work_start': '09:00',
                'work_end': '18:00',
                'timezone': 'Asia/Shanghai',
                'holiday_mode': False,
                'work_hours_response': '您好！我已为您转接人工客服，客服人员会尽快为您处理。',
                'non_work_hours_response': '您好！现在是非工作时间，您的问题已记录，客服人员会在工作时间内优先为您处理。',
                'weekend_response': '您好！现在是周末时间，您的问题已记录，客服人员会在下个工作日优先为您处理。',
                'holiday_response': '您好！现在是节假日期间，您的问题已记录，客服人员会在工作日恢复后优先为您处理。'
            }

        def register_plugin_functions(self, name, functions):
            pass

        def unregister_plugin_functions(self, name):
            pass

        def init_human_handover_tables(self):
            pass

        def get_handover_keywords(self):
            return [
                {'keyword': '转人工', 'priority': 1},
                {'keyword': '人工客服', 'priority': 2},
                {'keyword': '投诉', 'priority': 3}
            ]

        def get_work_schedule_config(self, config_key=None):
            """模拟获取工作时间配置"""
            if config_key:
                return {
                    'config_key': config_key,
                    'parsed_value': self.work_config.get(config_key)
                }
            else:
                return self.work_config

        def set_work_schedule_config(self, config_key, config_value, config_type='string', description=None):
            """模拟设置工作时间配置"""
            import json

            # 解析值
            if config_type == 'boolean':
                parsed_value = config_value.lower() in ('true', '1', 'yes', 'on')
            elif config_type == 'integer':
                parsed_value = int(config_value)
            elif config_type == 'json':
                parsed_value = json.loads(config_value)
            else:  # string
                parsed_value = config_value

            self.work_config[config_key] = parsed_value
            return True

    class MockHandler:
        def __init__(self):
            self.db = MockDB()

        def _log(self, message, level='INFO'):
            print(f"[{level}] {message}")

    # 导入插件
    from plugins.human_handover.plugin import HumanHandoverPlugin

    # 创建插件实例
    handler = MockHandler()
    plugin = HumanHandoverPlugin(handler)

    print(f"📋 当前配置:")
    print(f"   - 工作时间功能: {'启用' if plugin.work_schedule_enabled else '禁用'}")
    print(f"   - 工作日: {plugin.work_days}")
    print(f"   - 工作时间: {plugin.work_start} - {plugin.work_end}")
    print(f"   - 节假日模式: {'是' if plugin.holiday_mode else '否'}")

    # 测试当前时间
    now = datetime.now()
    is_work_time, time_type = plugin._is_work_time()

    print(f"\n🕐 当前时间测试:")
    print(f"   - 当前时间: {now.strftime('%Y-%m-%d %H:%M:%S')} (周{now.isoweekday()})")
    print(f"   - 是否工作时间: {'是' if is_work_time else '否'}")
    print(f"   - 时间类型: {time_type}")

    # 获取响应消息
    response = plugin._get_handover_response("转人工")
    print(f"   - 响应消息: {response}")

    # 测试不同时间场景
    print(f"\n📅 不同时间场景测试:")

    # 模拟不同的时间设置
    test_scenarios = [
        {
            "name": "工作日上班时间",
            "work_days": [1, 2, 3, 4, 5],
            "work_start": "09:00",
            "work_end": "18:00",
            "holiday_mode": False,
            "current_weekday": 2,  # 周二
            "current_hour": 14,    # 下午2点
            "expected": ("work_hours", True)
        },
        {
            "name": "工作日下班时间",
            "work_days": [1, 2, 3, 4, 5],
            "work_start": "09:00",
            "work_end": "18:00",
            "holiday_mode": False,
            "current_weekday": 3,  # 周三
            "current_hour": 20,    # 晚上8点
            "expected": ("non_work_hours", False)
        },
        {
            "name": "周末时间",
            "work_days": [1, 2, 3, 4, 5],
            "work_start": "09:00",
            "work_end": "18:00",
            "holiday_mode": False,
            "current_weekday": 6,  # 周六
            "current_hour": 14,    # 下午2点
            "expected": ("weekend", False)
        },
        {
            "name": "节假日模式",
            "work_days": [1, 2, 3, 4, 5],
            "work_start": "09:00",
            "work_end": "18:00",
            "holiday_mode": True,
            "current_weekday": 2,  # 周二
            "current_hour": 14,    # 下午2点
            "expected": ("holiday", False)
        }
    ]

    for scenario in test_scenarios:
        print(f"\n   🔍 场景: {scenario['name']}")

        # 临时修改插件配置
        original_work_days = plugin.work_days
        original_work_start = plugin.work_start
        original_work_end = plugin.work_end
        original_holiday_mode = plugin.holiday_mode

        plugin.work_days = scenario['work_days']
        plugin.work_start = scenario['work_start']
        plugin.work_end = scenario['work_end']
        plugin.holiday_mode = scenario['holiday_mode']

        # 模拟时间判断（简化版本，不修改系统时间）
        weekday = scenario['current_weekday']
        hour = scenario['current_hour']

        # 手动判断逻辑
        if plugin.holiday_mode:
            result_type = 'holiday'
            result_is_work = False
        elif weekday not in plugin.work_days:
            result_type = 'weekend'
            result_is_work = False
        else:
            start_hour = int(plugin.work_start.split(':')[0])
            end_hour = int(plugin.work_end.split(':')[0])
            if start_hour <= hour <= end_hour:
                result_type = 'work_hours'
                result_is_work = True
            else:
                result_type = 'non_work_hours'
                result_is_work = False

        expected_type, expected_is_work = scenario['expected']

        print(f"      - 模拟时间: 周{weekday} {hour}:00")
        print(f"      - 预期结果: {expected_type} ({'工作时间' if expected_is_work else '非工作时间'})")
        print(f"      - 实际结果: {result_type} ({'工作时间' if result_is_work else '非工作时间'})")
        print(f"      - 测试结果: {'✅ 通过' if (result_type == expected_type and result_is_work == expected_is_work) else '❌ 失败'}")

        # 恢复原始配置
        plugin.work_days = original_work_days
        plugin.work_start = original_work_start
        plugin.work_end = original_work_end
        plugin.holiday_mode = original_holiday_mode

    # 测试响应消息
    print(f"\n💬 响应消息测试:")
    response_mapping = {
        'work_hours': plugin.work_hours_response,
        'non_work_hours': plugin.non_work_hours_response,
        'weekend': plugin.weekend_response,
        'holiday': plugin.holiday_response
    }

    for time_type, expected_response in response_mapping.items():
        print(f"   - {time_type}: {expected_response[:50]}...")

    print(f"\n🎉 工作时间功能测试完成！")

if __name__ == "__main__":
    test_work_schedule()
