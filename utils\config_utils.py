# utils/config_utils.py
import os
import json

CONFIG_FILE = os.path.join(os.path.dirname(__file__), '..', 'config.json')


def load_config():
    try:
        with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ 配置文件不存在")
        return {}
    except json.JSONDecodeError:
        print("❌ 配置文件格式错误")
        return {}
    except Exception as e:
        print(f"❌ 加载配置失败: {e}")
        return {}