import json
import requests
import logging
from typing import List, Optional

from . import Plugin

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('AIChatPlugin')

class AIChatPlugin(Plugin):
    priority = 1  # 最高优先级之一（仅次于黑名单等系统级插件）

    def __init__(self, handler):
        super().__init__(handler)
        self.api_key = "app-E2GNsivt88KRwDgR0hB61IbN"  # 替换为你的有效 API Key
        self.url = "http://111.230.24.48:81/v1/chat-messages"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

    def on_messages(self, chat: str, messages: List[str]) -> Optional[str]:
        """
        接收消息后调用，返回要发送的回复内容或 None 表示不处理
        :param chat: 聊天窗口名称
        :param messages: 收到的消息列表
        :return: 回复内容 或 None
        """
        last_msg = messages[-1].strip()

        # 判断是否触发 AI 模式（可以修改为关键词匹配）
        if not last_msg.startswith(("AI", "/ai", "@春")):
            return None

        user_query = last_msg[3:].strip()  # 去除前缀
        if not user_query:
            return "请在 'AI' 后输入您的问题"

        logger.info(f"[AIChatPlugin] 在聊天 '{chat}' 中检测到AI指令，开始调用API...")
        ai_response = self.get_ai_reply(user_query)

        if ai_response:
            return ai_response.strip()
        else:
            return "抱歉，暂时无法获取AI回复，请稍后再试"

    def get_ai_reply(self, user_message: str) -> str:
        """
        获取AI回复（适配新的流式输出格式）
        """
        payload = {
            "inputs": {},
            "query": user_message,
            "response_mode": "streaming",
            "user": "terminal_user"
        }

        final_answer = ""
        try:
            logger.debug("向Dify API发送请求...")
            with requests.post(self.url, headers=self.headers, json=payload, stream=True, timeout=60) as response:
                if response.status_code == 200:
                    logger.debug("Dify API响应成功，开始处理流式响应")
                    for line in response.iter_lines():
                        if line:
                            try:
                                decoded_line = line.decode('utf-8')
                                if decoded_line.startswith("data:"):
                                    json_data = json.loads(decoded_line[5:].strip())
                                    event_type = json_data.get("event")

                                    # 处理AI回复内容（流式拼接）
                                    if event_type == "message" and "answer" in json_data:
                                        chunk = json_data["answer"]
                                        final_answer += chunk
                                        print(chunk, end="", flush=True)  # 实时打印AI的回复

                                    # 可选：记录token使用情况
                                    elif event_type == "message_end":
                                        logger.info("\nAI回复完成")
                                        if "metadata" in json_data:
                                            logger.info(f"Token使用情况: {json_data['metadata']['usage']}")

                            except Exception as e:
                                logger.warning(f"处理流式响应行失败: {e}")
                                continue
                else:
                    error_msg = f"AI接口请求失败，状态码：{response.status_code}"
                    logger.error(error_msg)
                    return error_msg
        except requests.Timeout:
            logger.error("Dify API请求超时")
            return "AI服务器响应超时，请稍后再试"
        except Exception as e:
            error_msg = f"AI接口异常：{str(e)}"
            logger.error(error_msg)
            return error_msg

        return final_answer