#!/usr/bin/env python3
"""
测试黑名单插件功能
"""

import os
import sys
from utils.config_utils import load_config
from db_plugins.mysql import MySQLDB
from plugins.blacklist_plugin.sql import BLACKLIST_SQL_FUNCTIONS


def test_blacklist_plugin():
    """测试黑名单插件功能"""
    print("🧪 开始测试黑名单插件...")
    
    try:
        # 加载配置
        cfg = load_config()
        db_config = cfg.get("mysql", {})
        if not db_config:
            print("❌ 未找到数据库配置")
            return False
        
        # 连接数据库
        db = MySQLDB(**db_config)
        db.connect()
        
        # 注册SQL函数
        db.register_plugin_functions("TestBlacklist", BLACKLIST_SQL_FUNCTIONS)
        
        # 测试获取所有黑名单条目
        print("📋 测试获取所有黑名单条目...")
        blacklist_entries = db.get_all_blacklist_entries()
        print(f"✅ 找到 {len(blacklist_entries)} 个黑名单条目:")
        
        for entry in blacklist_entries:
            print(f"  - {entry['chat_name']} (类型: {entry['chat_type']}, 全局: {entry['is_global']})")
        
        # 测试查询特定聊天
        test_chat = "守望大佬爱学习"
        print(f"\n🔍 测试查询聊天 '{test_chat}'...")
        entry = db.get_blacklist_entry_by_chat(test_chat)
        if entry:
            print(f"✅ 找到黑名单条目: {entry}")
        else:
            print(f"❌ 未找到 '{test_chat}' 的黑名单条目")
        
        # 测试黑名单统计
        print("\n📊 测试黑名单统计...")
        stats = db.get_blacklist_stats()
        print(f"✅ 黑名单统计: {stats}")
        
        # 模拟BlacklistPlugin的缓存加载逻辑
        print("\n🔄 模拟BlacklistPlugin缓存加载...")
        cache = {
            "group_blacklist": set(),
            "private_blacklist": set(),
            "global_blacklist": set()
        }
        
        for entry in blacklist_entries:
            chat_name = entry["chat_name"]
            chat_type = entry["chat_type"]
            is_global = entry["is_global"]
            
            if is_global:
                cache["global_blacklist"].add(chat_name)
            elif chat_type == "group":
                cache["group_blacklist"].add(chat_name)
            else:
                cache["private_blacklist"].add(chat_name)
        
        print(f"✅ 缓存加载完成:")
        print(f"  - 全局黑名单: {cache['global_blacklist']}")
        print(f"  - 群聊黑名单: {cache['group_blacklist']}")
        print(f"  - 私聊黑名单: {cache['private_blacklist']}")
        
        # 测试黑名单检查逻辑
        print(f"\n🔍 测试黑名单检查逻辑 ('{test_chat}')...")
        is_blacklisted = False
        
        if test_chat in cache["global_blacklist"]:
            print(f"✅ '{test_chat}' 在全局黑名单中")
            is_blacklisted = True
        elif test_chat in cache["group_blacklist"] or test_chat in cache["private_blacklist"]:
            print(f"✅ '{test_chat}' 在具体黑名单中")
            is_blacklisted = True
        else:
            print(f"❌ '{test_chat}' 不在黑名单中")
        
        if is_blacklisted:
            print(f"🚫 '{test_chat}' 应该被黑名单插件拦截")
        else:
            print(f"✅ '{test_chat}' 不会被黑名单插件拦截")
        
        # 关闭数据库连接
        db.close()
        
        print("\n✅ 黑名单插件测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_plugin_discovery():
    """测试插件发现机制"""
    print("\n🔍 测试插件发现机制...")
    
    try:
        from plugins import discover_plugins
        plugin_classes = discover_plugins()
        
        blacklist_plugins = []
        for cls in plugin_classes:
            if 'blacklist' in cls.__name__.lower():
                blacklist_plugins.append(cls)
        
        print(f"✅ 发现 {len(blacklist_plugins)} 个黑名单相关插件:")
        for cls in blacklist_plugins:
            print(f"  - {cls.__name__} (优先级: {getattr(cls, 'priority', 100)})")
        
        return True
        
    except Exception as e:
        print(f"❌ 插件发现测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🧪 黑名单插件测试工具")
    print("=" * 50)
    
    # 测试插件发现
    test_plugin_discovery()
    
    # 测试黑名单功能
    test_blacklist_plugin()
