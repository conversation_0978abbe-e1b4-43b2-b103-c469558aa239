"""
AI Chat Plugin SQL Functions
包含AI聊天插件所需的所有SQL查询函数
"""
from typing import Optional, List, Dict


def get_all_ai_keyword_rules_with_agent(self) -> List[Dict]:
    """
    获取 AI 插件使用的关键词与智能体绑定关系
    （ai_keyword_rules + ai_keyword_rules_collection + ai_agent_profiles）
    """
    cursor = self.connection.cursor(dictionary=True)
    cursor.execute("""
        SELECT akrr.chat_name, akrr.chat_type, akrr.global_rule,
               akrc.user_keyword, akrc.agent_id, akrc.reply_prompt
        FROM ai_keyword_rules akrr
        JOIN ai_keyword_rules_collection akrc ON akrr.id = akrc.keyword_id
        WHERE akrr.enabled = TRUE AND akrc.user_keyword IS NOT NULL
    """)
    return cursor.fetchall()


def get_ai_agent_by_id(self, agent_id: int) -> Optional[Dict]:
    """获取指定 agent_id 的 AI 智能体配置"""
    cursor = self.connection.cursor(dictionary=True)
    cursor.execute("""
        SELECT * FROM ai_agent_profiles
        WHERE id = %s
    """, (agent_id,))
    return cursor.fetchone()


def get_global_ai_agent(self, chat_type: str = "group") -> Optional[Dict]:
    """获取全局 AI 智能体配置（按群聊/私聊）"""
    cursor = self.connection.cursor(dictionary=True)
    cursor.execute("""
        SELECT aau.*, aap.api_key, aap.url, aap.model_type
        FROM ai_agent_usage aau
        JOIN ai_agent_profiles aap ON aau.agent_id = aap.id
        WHERE aau.global_rule = TRUE AND aau.chat_type = %s
    """, (chat_type,))
    return cursor.fetchone()


def get_ai_agent_usage_by_chat(self, chat_name: str) -> Optional[Dict]:
    """获取指定聊天窗口使用的 AI 智能体配置（如自定义模型）"""
    cursor = self.connection.cursor(dictionary=True)
    cursor.execute("""
        SELECT aau.*, aap.api_key, aap.url, aap.model_type
        FROM ai_agent_usage aau
        JOIN ai_agent_profiles aap ON aau.agent_id = aap.id
        WHERE aau.chat_name = %s
    """, (chat_name,))
    return cursor.fetchone()


def get_ai_agent_profiles_all(self) -> List[Dict]:
    """获取所有AI智能体配置"""
    cursor = self.connection.cursor(dictionary=True)
    cursor.execute("""
        SELECT * FROM ai_agent_profiles
        ORDER BY id
    """)
    return cursor.fetchall()


def create_ai_agent_profile(self, name: str, description: str, api_key: str, url: str, model_type: str) -> int:
    """创建新的AI智能体配置"""
    cursor = self.connection.cursor()
    cursor.execute("""
        INSERT INTO ai_agent_profiles (name, description, api_key, url, model_type)
        VALUES (%s, %s, %s, %s, %s)
    """, (name, description, api_key, url, model_type))
    self.connection.commit()
    return cursor.lastrowid


def update_ai_agent_profile(self, agent_id: int, name: str = None, description: str = None, 
                          api_key: str = None, url: str = None, model_type: str = None) -> bool:
    """更新AI智能体配置"""
    updates = []
    params = []
    
    if name is not None:
        updates.append("name = %s")
        params.append(name)
    if description is not None:
        updates.append("description = %s")
        params.append(description)
    if api_key is not None:
        updates.append("api_key = %s")
        params.append(api_key)
    if url is not None:
        updates.append("url = %s")
        params.append(url)
    if model_type is not None:
        updates.append("model_type = %s")
        params.append(model_type)
    
    if not updates:
        return False
    
    params.append(agent_id)
    cursor = self.connection.cursor()
    cursor.execute(f"""
        UPDATE ai_agent_profiles 
        SET {', '.join(updates)}
        WHERE id = %s
    """, params)
    self.connection.commit()
    return cursor.rowcount > 0


def delete_ai_agent_profile(self, agent_id: int) -> bool:
    """删除AI智能体配置"""
    cursor = self.connection.cursor()
    cursor.execute("""
        DELETE FROM ai_agent_profiles WHERE id = %s
    """, (agent_id,))
    self.connection.commit()
    return cursor.rowcount > 0


# 导出所有SQL函数
AI_CHAT_SQL_FUNCTIONS = {
    'get_all_ai_keyword_rules_with_agent': get_all_ai_keyword_rules_with_agent,
    'get_ai_agent_by_id': get_ai_agent_by_id,
    'get_global_ai_agent': get_global_ai_agent,
    'get_ai_agent_usage_by_chat': get_ai_agent_usage_by_chat,
    'get_ai_agent_profiles_all': get_ai_agent_profiles_all,
    'create_ai_agent_profile': create_ai_agent_profile,
    'update_ai_agent_profile': update_ai_agent_profile,
    'delete_ai_agent_profile': delete_ai_agent_profile,
}
