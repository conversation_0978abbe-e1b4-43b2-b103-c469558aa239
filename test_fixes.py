#!/usr/bin/env python3
"""
测试修复的问题
1. DeepSeek API密钥配置问题
2. 定时消息时间计算错误
"""

import os
import sys
from datetime import datetime, timedelta, time, date

def test_deepseek_config():
    """测试DeepSeek配置读取"""
    print("🔍 测试DeepSeek配置读取...")
    
    try:
        from utils.config_utils import load_config
        config = load_config()
        
        deepseek_config = config.get('deepseek', {})
        api_key = deepseek_config.get('api_key')
        
        if api_key:
            print(f"✅ DeepSeek API密钥已配置: {api_key[:10]}...")
            return True
        else:
            print("❌ DeepSeek API密钥未配置")
            return False
            
    except Exception as e:
        print(f"❌ 测试DeepSeek配置失败: {e}")
        return False


def test_time_calculation():
    """测试时间计算函数"""
    print("\n🔍 测试时间计算函数...")
    
    try:
        from plugins.scheduled_message.sql import calculate_next_send_time
        
        # 测试每日调度
        print("📅 测试每日调度...")
        next_time = calculate_next_send_time(
            schedule_type="daily",
            schedule_time=time(9, 30)
        )
        print(f"  下次发送时间: {next_time}")
        
        # 测试间隔调度
        print("⏰ 测试间隔调度...")
        next_time = calculate_next_send_time(
            schedule_type="interval",
            interval_minutes=60
        )
        print(f"  下次发送时间: {next_time}")
        
        # 测试一次性调度
        print("📆 测试一次性调度...")
        future_date = date.today() + timedelta(days=1)
        next_time = calculate_next_send_time(
            schedule_type="once",
            schedule_date=future_date,
            schedule_time=time(14, 30)
        )
        print(f"  下次发送时间: {next_time}")
        
        print("✅ 时间计算函数测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 时间计算函数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_time_conversion():
    """测试时间类型转换"""
    print("\n🔍 测试时间类型转换...")
    
    try:
        # 模拟从数据库读取的timedelta类型
        schedule_time_delta = timedelta(hours=9, minutes=30)
        
        # 转换为time类型
        total_seconds = int(schedule_time_delta.total_seconds())
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        schedule_time = time(hours, minutes)
        
        print(f"  原始timedelta: {schedule_time_delta}")
        print(f"  转换后time: {schedule_time}")
        
        # 测试datetime.combine
        today = date.today()
        combined_datetime = datetime.combine(today, schedule_time)
        print(f"  组合后datetime: {combined_datetime}")
        
        print("✅ 时间类型转换测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 时间类型转换测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_database_connection():
    """测试数据库连接"""
    print("\n🔍 测试数据库连接...")
    
    try:
        from utils.config_utils import load_config
        from db_plugins.mysql import MySQLDB
        
        config = load_config()
        db_config = config.get("mysql", {})
        
        if not db_config:
            print("❌ 未找到数据库配置")
            return False
        
        db = MySQLDB(**db_config)
        db.connect()
        
        print("✅ 数据库连接成功")
        
        # 测试注册SQL函数
        from plugins.scheduled_message.sql import SCHEDULED_MESSAGE_SQL_FUNCTIONS
        db.register_plugin_functions("TestFixes", SCHEDULED_MESSAGE_SQL_FUNCTIONS)
        
        print("✅ SQL函数注册成功")
        
        # 清理
        db.unregister_plugin_functions("TestFixes")
        print("✅ SQL函数卸载成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("=" * 60)
    print("问题修复测试")
    print("=" * 60)
    
    tests = [
        ("DeepSeek配置读取", test_deepseek_config),
        ("时间计算函数", test_time_calculation),
        ("时间类型转换", test_time_conversion),
        ("数据库连接", test_database_connection),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！问题已修复。")
    else:
        print("⚠️ 部分测试失败，需要进一步检查。")


if __name__ == '__main__':
    main()
