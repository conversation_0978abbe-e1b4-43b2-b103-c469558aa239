import os
import json
from typing import List, Optional, Dict, Any
# 抽象基类 Plugin（假设你已有定义）
from core.plugin_base import Plugin
from db_plugins.mysql import MySQLDB  # 假设你有一个 MySQLDB 类来处理数据库操作
# 导入插件的SQL函数
from .sql import KEYWORD_REPLY_SQL_FUNCTIONS
class KeywordReplyPlugin(Plugin):
    """
    关键词回复插件：从 MySQL 数据库中读取规则。
    支持：
    - 区分群聊和私聊，默认规则不同
    - 每个聊天窗口单独启用/禁用
    默认优先级：10
    """

    priority = 10

    def __init__(self, handler):
        super().__init__(handler)

        # 加载数据库配置
        # config_path = os.path.join(
        #     os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'config.json'
        # )
        # config_path = r"E:\wechat_test_all\wechat_bot\config.json"  # 直接写绝对路径
        # 使用主处理程序提供的数据库连接
        self.db = getattr(handler, 'db', None)
        if not self.db:
            raise ValueError("❌ handler 未绑定数据库实例，无法继续")

        # 注册本插件所需的 SQL 函数
        self.db.register_plugin_functions("KeywordReplyPlugin", KEYWORD_REPLY_SQL_FUNCTIONS)


        # 缓存规则数据
        self.cache = self._load_all_rules_from_db()

    def __del__(self):
        """插件析构时卸载SQL函数"""
        if hasattr(self, 'db') and self.db:
            self.db.unregister_plugin_functions("KeywordReplyPlugin")

    def _load_all_rules_from_db(self) -> Dict[str, Any]:
        """从数据库加载所有关键词规则"""
        results = self.db.get_all_keyword_rules()

        cache = {
            "default_group_rule": {},
            "default_private_rule": {},
            "chat_specific_rules": {}
        }

        chat_data = {}

        # 先按 chat_name 分组整理数据
        for row in results:
            chat_name = row["chat_name"]
            chat_type = row["chat_type"]
            enabled = row["enabled"]
            use_global_rules = row["use_global_rules"]
            global_rule = row["global_rule"]
            keyword = row["user_keyword"]
            reply = row["default_reply"]

            if chat_name not in chat_data:
                chat_data[chat_name] = {
                    "type": chat_type,
                    "enabled": enabled,
                    "use_global_rules": use_global_rules,
                    "rules": {}
                }
            if keyword and reply:
                chat_data[chat_name]["rules"][keyword] = reply

        # 构建缓存
        for chat_name, data in chat_data.items():
            if data.get("global_rule", False):
                if data["type"] == "group":
                    cache["default_group_rule"].update(data["rules"])
                else:
                    cache["default_private_rule"].update(data["rules"])
            else:
                cache["chat_specific_rules"][chat_name] = {
                    "type": data["type"],
                    "enabled": data["enabled"],
                    "use_global_rules": data["use_global_rules"],
                    "rules": data["rules"]
                }

        return cache

    def on_messages(self, chat: str, messages: List[str]) -> Optional[str]:
        chat_config = self.cache["chat_specific_rules"].get(chat, {})
        chat_enabled = chat_config.get("enabled", True)
        use_global_rules = chat_config.get("use_global_rules", False)
        chat_type = chat_config.get("type", "group")  # 默认为群聊
        chat_rules = chat_config.get("rules", {})

        # 如果局部禁用但允许使用全局规则，则清空局部规则
        if not chat_enabled:
            if use_global_rules:
                chat_rules = {}
            else:
                return None

        # 尝试匹配局部规则
        reply = self._match_rules(chat_rules, messages, chat, is_global=False)
        if reply:
            return reply

        # 尝试匹配全局规则
        if use_global_rules:
            if chat_type == "group":
                global_rules = self.cache["default_group_rule"]
            else:
                global_rules = self.cache["default_private_rule"]
            reply = self._match_rules(global_rules, messages, chat, is_global=True)
            if reply:
                return reply

        return None

    def _match_rules(self, rules: dict, messages: List[str], chat: str, is_global: bool) -> Optional[str]:
        for msg in reversed(messages):
            for keyword, reply in rules.items():
                if keyword in msg:
                    source = "全局" if is_global else "局部"
                    rule_type = "群聊" if "group" in (self.cache["chat_specific_rules"].get(chat, {}).get("type", "group")) else "私聊"
                    self.handler._log(f"[关键词回复] 【{chat}】【{rule_type}】{source}匹配 '{keyword}'，回复：{reply}")
                    return reply
        return None