"""
DeepSeek API 客户端
用于调用DeepSeek Chat API进行文本分析和生成
"""
import json
import requests
from typing import Dict, List, Optional
import logging

logger = logging.getLogger(__name__)


class DeepSeekClient:
    """DeepSeek API客户端"""

    def __init__(self, api_key: str, base_url: str = "https://api.deepseek.com"):
        """
        初始化DeepSeek客户端

        Args:
            api_key: DeepSeek API密钥
            base_url: API基础URL
        """
        self.api_key = api_key
        self.base_url = base_url
        self.chat_url = f"{base_url}/chat/completions"

    def _make_request(self, messages: List[Dict], model: str = "deepseek-chat",
                     max_tokens: int = 2048, temperature: float = 0.7) -> Optional[str]:
        """
        发送请求到DeepSeek API

        Args:
            messages: 消息列表
            model: 模型名称
            max_tokens: 最大token数
            temperature: 温度参数

        Returns:
            API响应内容或None
        """
        try:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            data = {
                "messages": messages,
                "model": model,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "top_p": 1,
                "stream": False
            }

            response = requests.post(
                self.chat_url,
                headers=headers,
                json=data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("choices") and result["choices"][0].get("message"):
                    return result["choices"][0]["message"]["content"]
                else:
                    logger.error(f"DeepSeek API响应格式异常: {result}")
                    return None
            else:
                logger.error(f"DeepSeek API请求失败: {response.status_code} - {response.text}")
                return None

        except requests.exceptions.Timeout:
            logger.error("DeepSeek API请求超时")
            return None
        except requests.exceptions.RequestException as e:
            logger.error(f"DeepSeek API请求异常: {e}")
            return None
        except Exception as e:
            logger.error(f"DeepSeek API调用失败: {e}")
            return None

    def generate_context_summary(self, user_name: str, conversation_history: List[Dict],
                               trigger_message: str) -> str:
        """
        生成对话上下文总结

        Args:
            user_name: 用户名
            conversation_history: 对话历史
            trigger_message: 触发消息

        Returns:
            上下文总结
        """
        try:
            # 构建对话历史文本
            history_text = ""
            for msg in conversation_history[-10:]:  # 只取最近10条
                sender = msg.get('sender', '用户')
                content = msg.get('message_content', '')
                timestamp = msg.get('created_at', '')
                history_text += f"[{timestamp}] {sender}: {content}\n"

            # 构建提示词
            system_prompt = """你是一个专业的客服助手，需要分析用户对话并生成简洁的上下文总结。
请根据用户的对话历史和触发转人工的消息，生成一个简洁明了的总结，包括：
1. 用户的主要问题或需求
2. 用户的情感状态（如：满意、不满、焦虑等）
3. 问题的紧急程度
4. 建议的处理方式

总结应该控制在100字以内，重点突出关键信息。"""

            user_prompt = f"""用户名: {user_name}
触发转人工的消息: {trigger_message}

最近对话历史:
{history_text}

请生成上下文总结："""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            summary = self._make_request(messages, temperature=0.3)

            if summary:
                return summary.strip()
            else:
                # 如果API调用失败，返回基础总结
                return f"用户 {user_name} 触发转人工关键词: {trigger_message}，需要人工处理。"

        except Exception as e:
            logger.error(f"生成上下文总结失败: {e}")
            return f"用户 {user_name} 触发转人工关键词: {trigger_message}，需要人工处理。"

    def generate_user_tags(self, user_name: str, conversation_history: List[Dict],
                          trigger_message: str, context_summary: str) -> List[Dict]:
        """
        生成用户标签

        Args:
            user_name: 用户名
            conversation_history: 对话历史
            trigger_message: 触发消息
            context_summary: 上下文总结

        Returns:
            用户标签列表
        """
        try:
            # 构建对话历史文本
            history_text = ""
            for msg in conversation_history[-5:]:  # 只取最近5条
                sender = msg.get('sender', '用户')
                content = msg.get('message_content', '')
                history_text += f"{sender}: {content}\n"

            # 构建提示词
            system_prompt = """你是一个专业的用户标签分析师，需要根据用户对话生成准确的标签。
请根据用户的对话历史、触发消息和上下文总结，生成3-5个用户标签。

标签类型包括：
1. 问题类型：投诉、退款、咨询、技术故障、建议反馈等
2. 情感倾向：满意、不满、焦虑、愤怒、中性等
3. 用户类型：新用户、老用户、VIP用户、普通用户等
4. 紧急程度：紧急、一般、不紧急等
5. 咨询时间：工作时间、非工作时间等

请以JSON格式返回，每个标签包含tag_name、tag_value和confidence（0-1之间的置信度）。
示例：[{"tag_name": "问题类型", "tag_value": "投诉", "confidence": 0.9}]"""

            user_prompt = f"""用户名: {user_name}
触发消息: {trigger_message}
上下文总结: {context_summary}

最近对话:
{history_text}

请生成用户标签（JSON格式）："""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            tags_response = self._make_request(messages, temperature=0.2)

            if tags_response:
                try:
                    # 清理响应内容，移除markdown代码块标记
                    cleaned_response = self._clean_json_response(tags_response)

                    # 尝试解析JSON
                    tags = json.loads(cleaned_response)
                    if isinstance(tags, list):
                        return tags
                    else:
                        logger.warning(f"DeepSeek返回的标签格式不正确: {tags_response}")
                except json.JSONDecodeError:
                    logger.warning(f"DeepSeek返回的标签无法解析为JSON: {tags_response}")

            # 如果API调用失败或解析失败，返回基础标签
            return self._generate_fallback_tags(trigger_message, conversation_history)

        except Exception as e:
            logger.error(f"生成用户标签失败: {e}")
            return self._generate_fallback_tags(trigger_message, conversation_history)

    def _clean_json_response(self, response: str) -> str:
        """
        清理DeepSeek API响应，移除markdown代码块标记

        Args:
            response: 原始响应内容

        Returns:
            清理后的JSON字符串
        """
        # 移除markdown代码块标记
        response = response.strip()

        # 移除开头的```json或```
        if response.startswith('```json'):
            response = response[7:]
        elif response.startswith('```'):
            response = response[3:]

        # 移除结尾的```
        if response.endswith('```'):
            response = response[:-3]

        # 移除首尾空白字符
        return response.strip()

    def _generate_fallback_tags(self, trigger_message: str, conversation_history: List[Dict]) -> List[Dict]:
        """
        生成备用标签（当API调用失败时使用）

        Args:
            trigger_message: 触发消息
            conversation_history: 对话历史

        Returns:
            备用标签列表
        """
        tags = []

        # 基于触发关键词的标签
        keyword_tags = {
            '投诉': ('问题类型', '投诉'),
            '退款': ('问题类型', '退款'),
            '故障': ('问题类型', '技术故障'),
            '咨询': ('问题类型', '咨询'),
            '建议': ('问题类型', '建议反馈')
        }

        for keyword, (tag_name, tag_value) in keyword_tags.items():
            if keyword in trigger_message:
                tags.append({
                    'tag_name': tag_name,
                    'tag_value': tag_value,
                    'confidence': 0.8
                })

        # 基于对话历史的标签
        if conversation_history:
            msg_count = len(conversation_history)
            if msg_count > 20:
                tags.append({
                    'tag_name': '用户活跃度',
                    'tag_value': '高频用户',
                    'confidence': 0.7
                })
            elif msg_count > 5:
                tags.append({
                    'tag_name': '用户活跃度',
                    'tag_value': '普通用户',
                    'confidence': 0.6
                })
            else:
                tags.append({
                    'tag_name': '用户活跃度',
                    'tag_value': '新用户',
                    'confidence': 0.5
                })

        # 默认标签
        if not tags:
            tags.append({
                'tag_name': '问题类型',
                'tag_value': '转人工',
                'confidence': 0.9
            })

        return tags
