# 消息分段回复插件使用说明

## 功能概述

消息分段回复插件是一个全局插件，用于控制微信机器人回复消息的分段方式。当回复内容较长时，可以自动将消息分段发送，避免单条消息过长影响阅读体验。

## 主要特性

### 🎯 多种分段模式
- **none**: 不分段，全部输出
- **force**: 强制分段回复（超过50字就分段）
- **auto**: 根据字数自动分段（默认模式）
- **probability**: 概率分段回复

### ⚙️ 灵活配置
- 可配置字数阈值（默认500字）
- 可设置分段概率（0-1之间）
- 可调整分段间隔时间（默认1.5秒）
- 支持全局启用/禁用

### 🔧 智能分段
- 优先按句子分割（句号、问号、感叹号）
- 避免在词语中间断开
- 自动添加"(续)"标识
- 保持@用户信息在首段

## 配置说明

### 配置文件位置
配置信息位于 `config.json` 文件中的 `message_segmentation` 部分：

```json
{
  "message_segmentation": {
    "enabled": true,
    "mode": "auto",
    "max_length": 500,
    "probability": 0.3,
    "segment_delay": 1.5,
    "description": "消息分段回复配置"
  }
}
```

### 配置参数详解

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enabled` | boolean | true | 是否启用分段功能 |
| `mode` | string | "auto" | 分段模式 |
| `max_length` | integer | 500 | 触发分段的字数阈值 |
| `probability` | float | 0.3 | 概率分段模式下的分段概率 |
| `segment_delay` | float | 1.5 | 分段消息间隔时间（秒） |

### 分段模式说明

#### 1. none - 不分段
```json
"mode": "none"
```
- 所有消息都不分段，完整发送
- 适用于希望保持消息完整性的场景

#### 2. force - 强制分段
```json
"mode": "force"
```
- 超过50字就强制分段
- 适用于希望所有长消息都分段的场景

#### 3. auto - 自动分段（推荐）
```json
"mode": "auto"
```
- 根据 `max_length` 设置自动判断是否分段
- 平衡了消息完整性和阅读体验

#### 4. probability - 概率分段
```json
"mode": "probability"
```
- 当消息超过阈值时，按概率决定是否分段
- 增加回复的随机性和自然感

## 使用示例

### 示例1：基础配置
```json
{
  "message_segmentation": {
    "enabled": true,
    "mode": "auto",
    "max_length": 300,
    "segment_delay": 2.0
  }
}
```
- 启用分段功能
- 自动模式，300字以上分段
- 分段间隔2秒

### 示例2：概率分段
```json
{
  "message_segmentation": {
    "enabled": true,
    "mode": "probability",
    "max_length": 400,
    "probability": 0.7,
    "segment_delay": 1.0
  }
}
```
- 启用概率分段
- 400字以上有70%概率分段
- 分段间隔1秒

### 示例3：禁用分段
```json
{
  "message_segmentation": {
    "enabled": false
  }
}
```
- 完全禁用分段功能
- 所有消息完整发送

## 分段效果示例

### 原始消息
```
这是一条很长的回复消息。包含了大量的信息和内容，需要用户仔细阅读。消息中包含了多个要点和详细说明，如果一次性发送可能会影响阅读体验。因此我们需要将其分段发送，让用户更容易理解和消化内容。
```

### 分段后效果
```
第一段：
这是一条很长的回复消息。包含了大量的信息和内容，需要用户仔细阅读。消息中包含了多个要点和详细说明，如果一次性发送可能会影响阅读体验。 (续)

[间隔1.5秒]

第二段：
因此我们需要将其分段发送，让用户更容易理解和消化内容。
```

## 技术实现

### 插件架构
- 继承自 `Plugin` 基类
- 优先级：-100（最低优先级，作为全局处理器）
- 不直接处理消息，提供分段发送工具方法

### 分段算法
1. **句子分割**：优先按标点符号分割
2. **长度控制**：确保每段不超过设定阈值
3. **智能合并**：避免产生过短的段落
4. **强制分割**：超长段落按字数强制分割

### 发送机制
- **群聊**：使用 `SendTypingText`（打字机效果）
- **私聊**：使用 `SendMsg`（普通发送）
- **回退机制**：发送失败时自动回退到备用方法

## 注意事项

### ⚠️ 重要提醒
1. **间隔时间**：不要设置过短的间隔时间，避免被微信限制
2. **字数阈值**：建议设置在300-800字之间
3. **概率设置**：概率分段模式下建议概率不超过0.8

### 🔧 故障排除
1. **插件未生效**：检查 `enabled` 是否为 `true`
2. **分段异常**：查看日志中的错误信息
3. **发送失败**：检查微信连接状态

## 测试方法

运行测试脚本验证功能：
```bash
python test_message_segmentation.py
```

测试内容包括：
- 配置文件加载
- 插件实例化
- 分段逻辑验证
- 配置信息获取

## 更新日志

### v1.0.0 (2024-01-XX)
- ✨ 初始版本发布
- 🎯 支持4种分段模式
- ⚙️ 完整的配置系统
- 🔧 智能分段算法
- 📝 完整的使用文档
