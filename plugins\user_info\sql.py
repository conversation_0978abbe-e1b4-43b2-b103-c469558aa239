def insert_or_update_group(self, group_name: str, member_count: int):
    cursor = self.connection.cursor()
    sql = """
        INSERT INTO wx_groups (group_name, member_count)
        VALUES (%s, %s)
        ON DUPLICATE KEY UPDATE
            member_count = VALUES(member_count)
    """
    try:
        cursor.execute(sql, (group_name, member_count))
        self.connection.commit()
    except Exception as e:
        self.connection.rollback()
        raise e


def insert_or_update_company_friend(self, company: str, nickname: str, true_name: str,
                                    phone: str, remark: str, common_groups: str):
    cursor = self.connection.cursor()
    sql = """
        INSERT INTO wx_company_friends
            (company, nickname, true_name, phone, remark, common_groups)
        VALUES (%s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
            phone = VALUES(phone),
            remark = VALUES(remark),
            common_groups = VALUES(common_groups)
    """
    try:
        cursor.execute(sql, (company, nickname, true_name, phone, remark, common_groups))
        self.connection.commit()
    except Exception as e:
        self.connection.rollback()
        raise e


def insert_company_tag(self, company: str, nickname: str, tag: str):
    cursor = self.connection.cursor()
    sql = """
        INSERT INTO wx_person_company_tags
            (target_type, company, nickname, tag_type, tag_name)
        VALUES ('company', %s, %s, 'system', %s)
        ON DUPLICATE KEY UPDATE
            tag_name = VALUES(tag_name)
    """
    try:
        cursor.execute(sql, (company, nickname, tag))
        self.connection.commit()
    except Exception as e:
        self.connection.rollback()
        raise e


def insert_or_update_personal_friend(self, wxid: str, nickname: str, remark: str,
                                     region: str, phone: str, signature: str,
                                     common_groups: str, source: str):
    cursor = self.connection.cursor()
    sql = """
        INSERT INTO wx_personal_friends
            (wxid, nickname, remark, region, phone, signature, common_groups, source)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
            remark = VALUES(remark),
            region = VALUES(region),
            phone = VALUES(phone),
            signature = VALUES(signature),
            common_groups = VALUES(common_groups),
            source = VALUES(source)
    """
    try:
        cursor.execute(sql, (wxid, nickname, remark, region, phone, signature, common_groups, source))
        self.connection.commit()
    except Exception as e:
        self.connection.rollback()
        raise e


def insert_personal_tag(self, wxid: str, tag: str):
    cursor = self.connection.cursor()
    sql = """
        INSERT INTO wx_person_company_tags
            (target_type, wxid, tag_type, tag_name)
        VALUES ('personal', %s, 'system', %s)
        ON DUPLICATE KEY UPDATE
            tag_name = VALUES(tag_name)
    """
    try:
        cursor.execute(sql, (wxid, tag.strip()))
        self.connection.commit()
    except Exception as e:
        self.connection.rollback()
        raise e

def record_program_status(self, status: str, timestamp: str = None):
    """记录程序运行状态（启动/停止）"""
    cursor = self.connection.cursor()
    if timestamp is None:
        timestamp = "NOW()"
        sql = f"""
            INSERT INTO program_status_log (status, timestamp)
            VALUES (%s, {timestamp})
        """
        cursor.execute(sql, (status,))
    else:
        sql = """
            INSERT INTO program_status_log (status, timestamp)
            VALUES (%s, %s)
        """
        cursor.execute(sql, (status, timestamp))

    try:
        self.connection.commit()
    except Exception as e:
        self.connection.rollback()
        raise e


def get_last_program_status(self):
    """获取最后一次程序状态记录"""
    cursor = self.connection.cursor()
    sql = """
        SELECT status, timestamp
        FROM program_status_log
        ORDER BY timestamp DESC
        LIMIT 1
    """
    cursor.execute(sql)
    result = cursor.fetchone()
    return result if result else None


def get_last_data_collection_time(self):
    """获取最后一次数据采集时间"""
    cursor = self.connection.cursor()
    sql = """
        SELECT last_collection_time
        FROM user_data_config
        WHERE id = 1
    """
    cursor.execute(sql)
    result = cursor.fetchone()
    return result[0] if result else None


def update_last_collection_time(self, timestamp: str = None):
    """更新最后一次数据采集时间"""
    cursor = self.connection.cursor()
    if timestamp is None:
        sql = """
            INSERT INTO user_data_config (id, last_collection_time, updated_at)
            VALUES (1, NOW(), NOW())
            ON DUPLICATE KEY UPDATE
                last_collection_time = NOW(),
                updated_at = NOW()
        """
        cursor.execute(sql)
    else:
        sql = """
            INSERT INTO user_data_config (id, last_collection_time, updated_at)
            VALUES (1, %s, NOW())
            ON DUPLICATE KEY UPDATE
                last_collection_time = %s,
                updated_at = NOW()
        """
        cursor.execute(sql, (timestamp, timestamp))

    try:
        self.connection.commit()
    except Exception as e:
        self.connection.rollback()
        raise e


def get_user_data_config(self):
    """获取用户数据采集配置"""
    cursor = self.connection.cursor()
    sql = """
        SELECT force_collection_on_startup, collection_interval_hours,
               last_collection_time, created_at, updated_at
        FROM user_data_config
        WHERE id = 1
    """
    cursor.execute(sql)
    result = cursor.fetchone()
    if result:
        return {
            'force_collection_on_startup': bool(result[0]),
            'collection_interval_hours': result[1],
            'last_collection_time': result[2],
            'created_at': result[3],
            'updated_at': result[4]
        }
    return None


def update_user_data_config(self, force_collection_on_startup: bool = None,
                           collection_interval_hours: int = None):
    """更新用户数据采集配置"""
    cursor = self.connection.cursor()

    # 构建动态更新SQL
    updates = []
    params = []

    if force_collection_on_startup is not None:
        updates.append("force_collection_on_startup = %s")
        params.append(force_collection_on_startup)

    if collection_interval_hours is not None:
        updates.append("collection_interval_hours = %s")
        params.append(collection_interval_hours)

    if not updates:
        return  # 没有要更新的内容

    updates.append("updated_at = NOW()")

    sql = f"""
        INSERT INTO user_data_config (id, force_collection_on_startup, collection_interval_hours, updated_at)
        VALUES (1, %s, %s, NOW())
        ON DUPLICATE KEY UPDATE
            {', '.join(updates)}
    """

    # 为INSERT准备参数
    insert_params = [
        force_collection_on_startup if force_collection_on_startup is not None else False,
        collection_interval_hours if collection_interval_hours is not None else 24
    ]

    cursor.execute(sql, insert_params + params)

    try:
        self.connection.commit()
    except Exception as e:
        self.connection.rollback()
        raise e


def init_user_data_tables(self):
    """初始化用户数据相关表"""
    cursor = self.connection.cursor()

    # 创建程序状态日志表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS program_status_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            status ENUM('startup', 'shutdown') NOT NULL,
            timestamp DATETIME NOT NULL,
            INDEX idx_timestamp (timestamp)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """)

    # 创建用户数据配置表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS user_data_config (
            id INT PRIMARY KEY DEFAULT 1,
            force_collection_on_startup BOOLEAN DEFAULT FALSE COMMENT '是否强制在启动时采集数据',
            collection_interval_hours INT DEFAULT 24 COMMENT '数据采集间隔（小时）',
            last_collection_time DATETIME NULL COMMENT '最后一次采集时间',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """)

    # 插入默认配置
    cursor.execute("""
        INSERT IGNORE INTO user_data_config (id, force_collection_on_startup, collection_interval_hours)
        VALUES (1, FALSE, 24)
    """)

    try:
        self.connection.commit()
    except Exception as e:
        self.connection.rollback()
        raise e


USER_INFO_SQL_FUNCTIONS = {
    'insert_or_update_group': insert_or_update_group,
    'insert_or_update_company_friend': insert_or_update_company_friend,
    'insert_company_tag': insert_company_tag,
    'insert_or_update_personal_friend': insert_or_update_personal_friend,
    'insert_personal_tag': insert_personal_tag,
    'record_program_status': record_program_status,
    'get_last_program_status': get_last_program_status,
    'get_last_data_collection_time': get_last_data_collection_time,
    'update_last_collection_time': update_last_collection_time,
    'get_user_data_config': get_user_data_config,
    'update_user_data_config': update_user_data_config,
    'init_user_data_tables': init_user_data_tables,
}