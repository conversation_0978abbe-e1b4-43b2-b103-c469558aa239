#!/usr/bin/env python3
"""
定时消息快速设置脚本
提供一些常用的定时消息模板
"""

import os
import sys
from utils.config_utils import load_config
from db_plugins.mysql import MySQLDB


class ScheduledMessageSetup:
    """定时消息快速设置"""
    
    def __init__(self):
        # 加载配置
        self.config = load_config()
        db_config = self.config.get("mysql", {})
        if not db_config:
            raise ValueError("❌ config.json 中未配置 mysql 数据库信息")
        
        # 连接数据库
        self.db = MySQLDB(**db_config)
        self.db.connect()
        
        # 注册SQL函数
        from plugins.scheduled_message.sql import SCHEDULED_MESSAGE_SQL_FUNCTIONS
        self.db.register_plugin_functions("ScheduledMessageSetup", SCHEDULED_MESSAGE_SQL_FUNCTIONS)
        
        # 初始化表
        try:
            self.db.init_scheduled_message_tables()
            print("✅ 数据库表初始化完成")
        except Exception as e:
            print(f"❌ 初始化数据库表失败: {e}")

    def setup_common_messages(self):
        """设置常用定时消息"""
        print("🚀 开始设置常用定时消息...")
        
        # 常用定时消息模板
        templates = [
            {
                "task_name": "早安问候",
                "chat_name": "测试群",  # 请修改为实际群名
                "chat_type": "group",
                "message_content": "🌅 早上好！新的一天开始了，祝大家工作顺利！",
                "schedule_type": "daily",
                "schedule_time": "08:30",
                "description": "每天早上8:30发送早安问候"
            },
            {
                "task_name": "午餐提醒",
                "chat_name": "测试群",
                "chat_type": "group", 
                "message_content": "🍽️ 午餐时间到了，记得按时吃饭哦！",
                "schedule_type": "daily",
                "schedule_time": "12:00",
                "description": "每天中午12:00提醒午餐"
            },
            {
                "task_name": "下班提醒",
                "chat_name": "测试群",
                "chat_type": "group",
                "message_content": "🏠 下班时间到了，记得关闭电脑，注意安全！",
                "schedule_type": "daily", 
                "schedule_time": "18:00",
                "description": "每天下午6:00提醒下班"
            },
            {
                "task_name": "周报提醒",
                "chat_name": "测试群",
                "chat_type": "group",
                "message_content": "📊 各位同事，请记得提交本周工作总结，截止时间今晚6点。",
                "schedule_type": "weekly",
                "schedule_time": "17:00",
                "schedule_weekday": 4,  # 周五
                "description": "每周五下午5:00提醒提交周报"
            },
            {
                "task_name": "月度会议提醒",
                "chat_name": "测试群",
                "chat_type": "group",
                "message_content": "📅 月度总结会议将在明天上午9:00举行，请大家准时参加。",
                "schedule_type": "monthly",
                "schedule_time": "17:00",
                "schedule_day": 28,  # 每月28日
                "description": "每月28日下午5:00提醒月度会议"
            },
            {
                "task_name": "系统状态检查",
                "chat_name": "监控群",  # 请修改为实际群名
                "chat_type": "group",
                "message_content": "🔍 系统运行正常，所有服务状态良好。",
                "schedule_type": "interval",
                "interval_minutes": 120,  # 每2小时
                "description": "每2小时发送系统状态信息"
            }
        ]
        
        print(f"📋 准备添加 {len(templates)} 个定时消息模板...")
        print("⚠️  请注意：需要将聊天名称修改为实际的群聊或私聊名称")
        print()
        
        for i, template in enumerate(templates, 1):
            print(f"{i}. {template['task_name']}")
            print(f"   描述: {template['description']}")
            print(f"   聊天: {template['chat_name']} ({template['chat_type']})")
            print(f"   消息: {template['message_content']}")
            
            # 询问是否添加
            while True:
                choice = input(f"   是否添加此任务? (y/n/s=跳过): ").lower().strip()
                if choice in ['y', 'yes']:
                    self._add_template_message(template)
                    break
                elif choice in ['n', 'no']:
                    print("   ❌ 取消添加")
                    return
                elif choice in ['s', 'skip']:
                    print("   ⏭️ 跳过此任务")
                    break
                else:
                    print("   请输入 y(是), n(否), 或 s(跳过)")
            print()

    def _add_template_message(self, template):
        """添加模板消息"""
        try:
            from datetime import time
            
            kwargs = {
                "task_name": template["task_name"],
                "chat_name": template["chat_name"],
                "chat_type": template["chat_type"],
                "message_content": template["message_content"],
                "schedule_type": template["schedule_type"]
            }
            
            # 根据调度类型添加相应参数
            if template["schedule_type"] in ["daily", "weekly", "monthly"]:
                hour, minute = map(int, template["schedule_time"].split(':'))
                kwargs["schedule_time"] = time(hour, minute)
                
                if template["schedule_type"] == "weekly":
                    kwargs["schedule_weekday"] = template["schedule_weekday"]
                elif template["schedule_type"] == "monthly":
                    kwargs["schedule_day"] = template["schedule_day"]
                    
            elif template["schedule_type"] == "interval":
                kwargs["interval_minutes"] = template["interval_minutes"]
            
            success = self.db.add_scheduled_message(**kwargs)
            
            if success:
                print(f"   ✅ 任务添加成功: {template['task_name']}")
            else:
                print(f"   ❌ 任务添加失败: {template['task_name']}")
                
        except Exception as e:
            print(f"   ❌ 添加任务异常: {e}")

    def setup_custom_message(self):
        """设置自定义定时消息"""
        print("🛠️ 自定义定时消息设置...")
        
        try:
            # 获取基本信息
            task_name = input("任务名称: ").strip()
            if not task_name:
                print("❌ 任务名称不能为空")
                return
            
            chat_name = input("聊天名称: ").strip()
            if not chat_name:
                print("❌ 聊天名称不能为空")
                return
            
            while True:
                chat_type = input("聊天类型 (group/private): ").strip().lower()
                if chat_type in ['group', 'private']:
                    break
                print("❌ 请输入 group 或 private")
            
            message_content = input("消息内容: ").strip()
            if not message_content:
                print("❌ 消息内容不能为空")
                return
            
            # 选择调度类型
            print("\n调度类型:")
            print("1. 每日定时")
            print("2. 每周定时")
            print("3. 间隔定时")
            print("4. 一次性定时")
            
            while True:
                try:
                    choice = int(input("请选择 (1-4): "))
                    if 1 <= choice <= 4:
                        break
                    print("❌ 请输入 1-4 之间的数字")
                except ValueError:
                    print("❌ 请输入有效数字")
            
            # 根据选择设置参数
            if choice == 1:  # 每日定时
                send_time = input("发送时间 (HH:MM): ").strip()
                self._add_daily_custom(task_name, chat_name, chat_type, message_content, send_time)
                
            elif choice == 2:  # 每周定时
                print("星期几: 0=周一, 1=周二, 2=周三, 3=周四, 4=周五, 5=周六, 6=周日")
                weekday = int(input("星期几 (0-6): "))
                send_time = input("发送时间 (HH:MM): ").strip()
                self._add_weekly_custom(task_name, chat_name, chat_type, message_content, weekday, send_time)
                
            elif choice == 3:  # 间隔定时
                interval = int(input("间隔分钟数: "))
                self._add_interval_custom(task_name, chat_name, chat_type, message_content, interval)
                
            elif choice == 4:  # 一次性定时
                send_datetime = input("发送时间 (YYYY-MM-DD HH:MM): ").strip()
                self._add_once_custom(task_name, chat_name, chat_type, message_content, send_datetime)
            
        except KeyboardInterrupt:
            print("\n❌ 用户取消操作")
        except Exception as e:
            print(f"❌ 设置失败: {e}")

    def _add_daily_custom(self, task_name, chat_name, chat_type, message_content, send_time):
        """添加自定义每日任务"""
        try:
            from datetime import time
            hour, minute = map(int, send_time.split(':'))
            schedule_time = time(hour, minute)
            
            success = self.db.add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_content=message_content,
                schedule_type='daily',
                schedule_time=schedule_time
            )
            
            if success:
                print(f"✅ 每日定时任务添加成功: {task_name}")
            else:
                print(f"❌ 任务添加失败")
                
        except Exception as e:
            print(f"❌ 添加每日任务失败: {e}")

    def _add_weekly_custom(self, task_name, chat_name, chat_type, message_content, weekday, send_time):
        """添加自定义每周任务"""
        try:
            from datetime import time
            hour, minute = map(int, send_time.split(':'))
            schedule_time = time(hour, minute)
            
            success = self.db.add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_content=message_content,
                schedule_type='weekly',
                schedule_time=schedule_time,
                schedule_weekday=weekday
            )
            
            if success:
                weekday_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
                print(f"✅ 每周定时任务添加成功: {task_name} (每{weekday_names[weekday]} {send_time})")
            else:
                print(f"❌ 任务添加失败")
                
        except Exception as e:
            print(f"❌ 添加每周任务失败: {e}")

    def _add_interval_custom(self, task_name, chat_name, chat_type, message_content, interval):
        """添加自定义间隔任务"""
        try:
            success = self.db.add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_content=message_content,
                schedule_type='interval',
                interval_minutes=interval
            )
            
            if success:
                print(f"✅ 间隔定时任务添加成功: {task_name} (每 {interval} 分钟)")
            else:
                print(f"❌ 任务添加失败")
                
        except Exception as e:
            print(f"❌ 添加间隔任务失败: {e}")

    def _add_once_custom(self, task_name, chat_name, chat_type, message_content, send_datetime):
        """添加自定义一次性任务"""
        try:
            from datetime import datetime
            send_dt = datetime.strptime(send_datetime, "%Y-%m-%d %H:%M")
            
            success = self.db.add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_content=message_content,
                schedule_type='once',
                schedule_date=send_dt.date(),
                schedule_time=send_dt.time(),
                max_send_count=1
            )
            
            if success:
                print(f"✅ 一次性定时任务添加成功: {task_name} ({send_datetime})")
            else:
                print(f"❌ 任务添加失败")
                
        except Exception as e:
            print(f"❌ 添加一次性任务失败: {e}")

    def close(self):
        """关闭数据库连接"""
        if self.db:
            self.db.close()


def main():
    """主函数"""
    print("🚀 定时消息快速设置工具")
    print("=" * 50)
    
    try:
        setup = ScheduledMessageSetup()
        
        while True:
            print("\n请选择操作:")
            print("1. 设置常用定时消息模板")
            print("2. 自定义定时消息")
            print("3. 退出")
            
            try:
                choice = int(input("请选择 (1-3): "))
                
                if choice == 1:
                    setup.setup_common_messages()
                elif choice == 2:
                    setup.setup_custom_message()
                elif choice == 3:
                    print("👋 再见！")
                    break
                else:
                    print("❌ 请输入 1-3 之间的数字")
                    
            except ValueError:
                print("❌ 请输入有效数字")
            except KeyboardInterrupt:
                print("\n👋 用户取消操作")
                break
                
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if 'setup' in locals():
            setup.close()


if __name__ == "__main__":
    main()
