#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真正的分段发送效果
"""

import sys
import os
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

class MockWeChat:
    """模拟微信发送接口"""
    
    def __init__(self):
        self.sent_messages = []
        self.message_count = 0
    
    def SendMsg(self, message, who=None):
        """模拟发送消息"""
        self.message_count += 1
        timestamp = time.strftime("%H:%M:%S")
        self.sent_messages.append({
            'id': self.message_count,
            'timestamp': timestamp,
            'chat': who,
            'message': message,
            'method': 'SendMsg'
        })
        print(f"📤 [{timestamp}] 消息 #{self.message_count} 发送到 '{who}': {message}")
    
    def SendTypingText(self, message, who=None):
        """模拟发送打字机消息"""
        self.message_count += 1
        timestamp = time.strftime("%H:%M:%S")
        self.sent_messages.append({
            'id': self.message_count,
            'timestamp': timestamp,
            'chat': who,
            'message': message,
            'method': 'SendTypingText'
        })
        print(f"📤 [{timestamp}] 消息 #{self.message_count} 发送到 '{who}' (打字机): {message}")
    
    def get_summary(self):
        """获取发送摘要"""
        return {
            'total_messages': len(self.sent_messages),
            'messages': self.sent_messages
        }

def test_segmentation_sending():
    """测试分段发送功能"""
    print("🚀 测试真正的分段发送功能")
    print("=" * 60)
    
    # 创建模拟handler
    class MockHandler:
        def _log(self, message, level='INFO'):
            print(f"[{level}] {message}")
    
    # 创建模拟微信实例
    mock_wx = MockWeChat()
    
    # 导入插件
    from plugins.message_segmentation.plugin import MessageSegmentationPlugin
    
    # 创建插件实例
    handler = MockHandler()
    plugin = MessageSegmentationPlugin(handler)
    
    print(f"📋 当前配置:")
    print(f"   - 启用状态: {plugin.enabled}")
    print(f"   - 分段模式: {plugin.mode}")
    print(f"   - 字数阈值: {plugin.max_length}")
    print(f"   - 间隔时间: {plugin.segment_delay}秒")
    
    # 测试用例
    test_cases = [
        {
            "name": "短消息测试",
            "chat": "测试群聊",
            "message": "这是一条短消息，不需要分段。",
            "at_users": ["用户A"]
        },
        {
            "name": "长消息分段测试",
            "chat": "技术讨论群",
            "message": (
                "关于微信机器人的分段回复功能，我想详细说明一下实现原理。"
                "首先，我们需要判断消息长度是否超过设定的阈值。"
                "如果超过阈值，就需要将消息按照句子进行智能分割。"
                "分割时要注意保持语义的完整性，避免在词语中间断开。"
                "每个分段都会作为独立的消息发送出去，这样用户就能看到多条连续的消息。"
                "第一条消息会包含@用户的信息，后续消息则不包含。"
                "为了让用户知道还有后续内容，我们会在非最后一条消息后面添加'(续)'标识。"
                "分段之间会有适当的时间间隔，模拟真实的打字和发送过程。"
                "这样的设计既保证了消息的完整传达，又提升了用户的阅读体验。"
            ),
            "at_users": ["张三", "李四"]
        },
        {
            "name": "私聊长消息测试",
            "chat": "私聊-小明",
            "message": (
                "你好！关于你询问的分段回复功能，我来详细解释一下。"
                "这个功能的主要目的是将长消息分成多条短消息发送。"
                "比如当AI生成的回复内容很长时，一次性发送可能会影响阅读体验。"
                "通过分段发送，用户可以逐条阅读，更容易理解和消化内容。"
                "系统会自动判断消息长度，超过阈值就会触发分段机制。"
                "每个分段都是独立的消息，就像人工逐条发送一样自然。"
            ),
            "at_users": None  # 私聊不需要@
        }
    ]
    
    # 执行测试
    for i, case in enumerate(test_cases, 1):
        print(f"\n{'='*60}")
        print(f"📝 测试 {i}: {case['name']}")
        print(f"{'='*60}")
        print(f"聊天窗口: {case['chat']}")
        print(f"原始消息长度: {len(case['message'])}字")
        print(f"@用户: {case['at_users'] if case['at_users'] else '无'}")
        print(f"消息预览: {case['message'][:100]}...")
        
        # 记录发送前的消息数量
        before_count = mock_wx.message_count
        
        print(f"\n🚀 开始发送...")
        
        # 调用分段发送
        plugin.send_segmented_message(
            wx=mock_wx,
            chat=case['chat'],
            message=case['message'],
            at_users=case['at_users']
        )
        
        # 统计发送结果
        after_count = mock_wx.message_count
        sent_count = after_count - before_count
        
        print(f"\n📊 发送统计:")
        print(f"   - 发送消息数: {sent_count} 条")
        print(f"   - 总消息数: {after_count} 条")
        
        # 显示发送的消息
        recent_messages = mock_wx.sent_messages[before_count:]
        for j, msg in enumerate(recent_messages, 1):
            print(f"   消息{j}: [{msg['timestamp']}] {msg['message'][:80]}...")
    
    # 最终统计
    print(f"\n{'='*60}")
    print("📈 最终统计")
    print(f"{'='*60}")
    
    summary = mock_wx.get_summary()
    print(f"总共发送消息: {summary['total_messages']} 条")
    
    print(f"\n📋 所有消息列表:")
    for msg in summary['messages']:
        print(f"  #{msg['id']} [{msg['timestamp']}] {msg['chat']}: {msg['message'][:60]}...")
    
    print(f"\n🎉 测试完成！")
    print("可以看到长消息被分成了多条独立的消息发送")

if __name__ == "__main__":
    test_segmentation_sending()
