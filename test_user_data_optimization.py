#!/usr/bin/env python3
"""
测试用户数据采集优化功能
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.config_utils import load_config
from db_plugins.mysql import MySQLDB
from plugins.user_info.sql import USER_INFO_SQL_FUNCTIONS


def test_database_functions():
    """测试数据库功能"""
    print("🧪 开始测试数据库功能...")
    
    try:
        # 加载配置
        cfg = load_config()
        db_config = cfg.get("mysql", {})
        if not db_config:
            print("❌ 未找到数据库配置")
            return False
        
        # 连接数据库
        db = MySQLDB(**db_config)
        db.connect()
        
        # 注册SQL函数
        db.register_plugin_functions("TestUserData", USER_INFO_SQL_FUNCTIONS)
        
        # 测试初始化表
        print("📋 测试初始化数据库表...")
        db.init_user_data_tables()
        print("✅ 数据库表初始化成功")
        
        # 测试记录程序状态
        print("📝 测试记录程序状态...")
        db.record_program_status('startup')
        print("✅ 程序启动状态记录成功")
        
        # 测试获取最后状态
        print("📖 测试获取最后程序状态...")
        last_status = db.get_last_program_status()
        if last_status:
            status, timestamp = last_status
            print(f"✅ 最后状态: {status} at {timestamp}")
        else:
            print("⚠️ 未找到状态记录")
        
        # 测试配置功能
        print("⚙️ 测试配置功能...")
        
        # 更新配置
        db.update_user_data_config(
            force_collection_on_startup=True,
            collection_interval_hours=12
        )
        print("✅ 配置更新成功")
        
        # 获取配置
        config = db.get_user_data_config()
        if config:
            print(f"✅ 配置读取成功:")
            print(f"   - 强制启动采集: {config['force_collection_on_startup']}")
            print(f"   - 采集间隔: {config['collection_interval_hours']} 小时")
            print(f"   - 最后采集时间: {config['last_collection_time']}")
        else:
            print("⚠️ 未找到配置")
        
        # 测试更新采集时间
        print("🕒 测试更新采集时间...")
        db.update_last_collection_time()
        print("✅ 采集时间更新成功")
        
        # 再次获取配置验证
        config = db.get_user_data_config()
        if config and config['last_collection_time']:
            print(f"✅ 验证采集时间: {config['last_collection_time']}")
        
        # 测试记录停止状态
        print("🛑 测试记录程序停止状态...")
        db.record_program_status('shutdown')
        print("✅ 程序停止状态记录成功")
        
        # 清理
        db.close()
        print("✅ 所有数据库功能测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 数据库功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_time_logic():
    """测试时间逻辑"""
    print("\n🧪 开始测试时间逻辑...")
    
    try:
        # 模拟时间计算逻辑
        now = datetime.now()
        
        # 测试场景1: 首次运行
        last_collection_time = None
        interval_hours = 24
        
        if not last_collection_time:
            print("✅ 场景1 - 首次运行: 应该采集数据")
        
        # 测试场景2: 时间未到
        last_collection_time = now - timedelta(hours=2)
        time_diff = now - last_collection_time
        
        if time_diff.total_seconds() < interval_hours * 3600:
            remaining_time = timedelta(hours=interval_hours) - time_diff
            print(f"✅ 场景2 - 时间未到: 距离上次采集 {time_diff}，还需等待 {remaining_time}")
        
        # 测试场景3: 时间已到
        last_collection_time = now - timedelta(hours=25)
        time_diff = now - last_collection_time
        
        if time_diff.total_seconds() >= interval_hours * 3600:
            print(f"✅ 场景3 - 时间已到: 距离上次采集 {time_diff}，应该采集数据")
        
        print("✅ 时间逻辑测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 时间逻辑测试失败: {e}")
        return False


def test_config_manager():
    """测试配置管理器"""
    print("\n🧪 开始测试配置管理器...")
    
    try:
        from user_data_config_manager import UserDataConfigManager
        
        # 创建管理器实例
        manager = UserDataConfigManager()
        print("✅ 配置管理器创建成功")
        
        # 测试显示配置
        print("📋 测试显示当前配置...")
        manager.show_current_config()
        
        # 测试设置强制采集
        print("🔄 测试设置强制采集...")
        manager.set_force_collection(True)
        
        # 测试设置采集间隔
        print("⏰ 测试设置采集间隔...")
        manager.set_collection_interval(6)
        
        # 测试显示状态日志
        print("📊 测试显示状态日志...")
        manager.show_program_status_log(5)
        
        print("✅ 配置管理器测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始用户数据采集优化功能测试")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    # 测试数据库功能
    if test_database_functions():
        success_count += 1
    
    # 测试时间逻辑
    if test_time_logic():
        success_count += 1
    
    # 测试配置管理器
    if test_config_manager():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过! 用户数据采集优化功能正常工作")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"❌ 测试程序异常: {e}")
        import traceback
        traceback.print_exc()
