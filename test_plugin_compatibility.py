#!/usr/bin/env python3
"""
测试插件兼容性 - 验证WeChatUserInfoPlugin是否有on_messages方法
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_plugin_compatibility():
    """测试插件兼容性"""
    print("🧪 开始测试插件兼容性...")
    
    try:
        # 模拟handler对象
        class MockHandler:
            def __init__(self):
                self.wx = None
                self.db = None
                
            def _log(self, msg, level="INFO"):
                print(f"[MockHandler] [{level}] {msg}")
        
        # 模拟数据库对象
        class MockDB:
            def register_plugin_functions(self, name, functions):
                print(f"✅ 注册插件函数: {name}")
                
            def init_user_data_tables(self):
                print("✅ 初始化数据库表")
                
            def record_program_status(self, status):
                print(f"✅ 记录程序状态: {status}")
                
            def get_user_data_config(self):
                return {
                    'force_collection_on_startup': False,
                    'collection_interval_hours': 24,
                    'last_collection_time': None,
                    'created_at': None,
                    'updated_at': None
                }
                
            def update_last_collection_time(self):
                print("✅ 更新最后采集时间")
        
        # 创建模拟handler
        handler = MockHandler()
        handler.db = MockDB()
        
        # 导入插件类
        from plugins.user_info.plugin import WeChatUserInfoPlugin
        
        print("📦 导入插件类成功")
        
        # 检查是否有on_messages方法
        if hasattr(WeChatUserInfoPlugin, 'on_messages'):
            print("✅ WeChatUserInfoPlugin 有 on_messages 方法")
        else:
            print("❌ WeChatUserInfoPlugin 缺少 on_messages 方法")
            return False
        
        # 测试方法签名
        import inspect
        sig = inspect.signature(WeChatUserInfoPlugin.on_messages)
        params = list(sig.parameters.keys())
        expected_params = ['self', 'chat', 'messages']
        
        if params == expected_params:
            print("✅ on_messages 方法签名正确")
        else:
            print(f"❌ on_messages 方法签名错误: 期望 {expected_params}, 实际 {params}")
            return False
        
        # 测试返回类型注解
        return_annotation = sig.return_annotation
        if 'Optional' in str(return_annotation) and 'str' in str(return_annotation):
            print("✅ on_messages 返回类型注解正确")
        else:
            print(f"⚠️ on_messages 返回类型注解: {return_annotation}")
        
        # 测试调用方法
        try:
            # 由于我们不能真正实例化插件（需要真实的数据库连接），
            # 我们只测试方法是否存在和可调用
            method = getattr(WeChatUserInfoPlugin, 'on_messages')
            if callable(method):
                print("✅ on_messages 方法可调用")
            else:
                print("❌ on_messages 方法不可调用")
                return False
        except Exception as e:
            print(f"❌ 测试on_messages方法时出错: {e}")
            return False
        
        print("✅ 插件兼容性测试通过!")
        return True
        
    except ImportError as e:
        print(f"❌ 导入插件失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 插件兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_plugin_loading():
    """测试插件加载系统"""
    print("\n🧪 开始测试插件加载系统...")
    
    try:
        from plugins import discover_plugins, PLUGIN_BASE_CLASSES
        
        print("📦 导入插件加载系统成功")
        
        # 发现插件
        plugin_classes = discover_plugins()
        print(f"✅ 发现 {len(plugin_classes)} 个插件类")
        
        # 检查WeChatUserInfoPlugin是否被发现
        user_info_plugin_found = False
        for cls in plugin_classes:
            if cls.__name__ == 'WeChatUserInfoPlugin':
                user_info_plugin_found = True
                print(f"✅ 找到 WeChatUserInfoPlugin: {cls}")
                
                # 检查基类
                for base_class in PLUGIN_BASE_CLASSES:
                    if issubclass(cls, base_class):
                        print(f"✅ WeChatUserInfoPlugin 继承自: {base_class.__name__}")
                        break
                
                # 检查是否有on_messages方法
                if hasattr(cls, 'on_messages'):
                    print("✅ WeChatUserInfoPlugin 有 on_messages 方法")
                else:
                    print("❌ WeChatUserInfoPlugin 缺少 on_messages 方法")
                    return False
                
                break
        
        if not user_info_plugin_found:
            print("❌ 未找到 WeChatUserInfoPlugin")
            return False
        
        print("✅ 插件加载系统测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 插件加载系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始插件兼容性测试")
    print("=" * 50)
    
    success_count = 0
    total_tests = 2
    
    # 测试插件兼容性
    if test_plugin_compatibility():
        success_count += 1
    
    # 测试插件加载系统
    if test_plugin_loading():
        success_count += 1
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有兼容性测试通过! 插件应该能正常工作")
        print("\n💡 修复说明:")
        print("- 为 WeChatUserInfoPlugin 添加了 on_messages 方法")
        print("- 该方法返回 None，表示不处理消息")
        print("- 这解决了系统调用 on_messages 时的 AttributeError")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"❌ 测试程序异常: {e}")
        import traceback
        traceback.print_exc()
