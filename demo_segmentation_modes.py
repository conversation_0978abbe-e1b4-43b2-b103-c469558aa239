#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示不同分段模式的效果
"""

import sys
import os
import json
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

def create_mock_handler():
    """创建模拟handler"""
    class MockHandler:
        def _log(self, message, level='INFO'):
            print(f"[{level}] {message}")
    return MockHandler()

def demo_mode(mode_name, config_override):
    """演示特定模式"""
    print(f"\n{'='*60}")
    print(f"🎯 演示模式: {mode_name}")
    print(f"{'='*60}")
    
    # 临时修改配置
    from utils.config_utils import load_config
    original_config = load_config()
    
    # 创建临时配置
    temp_config = original_config.copy()
    if 'message_segmentation' not in temp_config:
        temp_config['message_segmentation'] = {}
    temp_config['message_segmentation'].update(config_override)
    
    # 保存临时配置
    with open('config.json', 'w', encoding='utf-8') as f:
        json.dump(temp_config, f, ensure_ascii=False, indent=2)
    
    try:
        # 重新导入插件以应用新配置
        import importlib
        import plugins.message_segmentation.plugin
        importlib.reload(plugins.message_segmentation.plugin)
        
        from plugins.message_segmentation.plugin import MessageSegmentationPlugin
        
        # 创建插件实例
        handler = create_mock_handler()
        plugin = MessageSegmentationPlugin(handler)
        
        # 显示当前配置
        print(f"📋 当前配置:")
        print(f"   - 模式: {plugin.mode}")
        print(f"   - 阈值: {plugin.max_length}字")
        print(f"   - 概率: {plugin.probability}")
        print(f"   - 间隔: {plugin.segment_delay}秒")
        
        # 测试消息
        test_message = (
            "欢迎使用微信机器人的消息分段回复功能！这个功能可以帮助您更好地管理长消息的发送。"
            "当回复内容较长时，系统会自动将消息分成多个段落发送，这样可以提高用户的阅读体验。"
            "分段功能支持多种模式：不分段、强制分段、自动分段和概率分段。"
            "您可以根据实际需求选择合适的模式。同时，系统还支持自定义字数阈值、分段概率和间隔时间等参数。"
            "这样可以让机器人的回复更加人性化和智能化。希望这个功能能够为您的微信机器人带来更好的用户体验！"
        )
        
        print(f"\n📝 测试消息 (共{len(test_message)}字):")
        print(f"   {test_message[:100]}...")
        
        # 测试分段
        should_segment = plugin.should_segment(test_message)
        segments = plugin.segment_message(test_message)
        
        print(f"\n🔍 分段结果:")
        print(f"   - 需要分段: {should_segment}")
        print(f"   - 分段数量: {len(segments)}")
        
        for i, segment in enumerate(segments):
            print(f"\n   📄 段落 {i+1} ({len(segment)}字):")
            print(f"      {segment}")
            if i < len(segments) - 1:
                print(f"      [等待 {plugin.segment_delay} 秒...]")
        
    finally:
        # 恢复原始配置
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(original_config, f, ensure_ascii=False, indent=2)

def main():
    """主演示函数"""
    print("🚀 消息分段回复功能演示")
    print("本演示将展示不同配置下的分段效果")
    
    # 演示不同模式
    demos = [
        {
            "name": "不分段模式",
            "config": {
                "enabled": True,
                "mode": "none",
                "max_length": 500
            }
        },
        {
            "name": "强制分段模式",
            "config": {
                "enabled": True,
                "mode": "force",
                "max_length": 500,
                "segment_delay": 1.0
            }
        },
        {
            "name": "自动分段模式 (300字阈值)",
            "config": {
                "enabled": True,
                "mode": "auto",
                "max_length": 300,
                "segment_delay": 1.5
            }
        },
        {
            "name": "自动分段模式 (150字阈值)",
            "config": {
                "enabled": True,
                "mode": "auto",
                "max_length": 150,
                "segment_delay": 2.0
            }
        },
        {
            "name": "概率分段模式 (70%概率)",
            "config": {
                "enabled": True,
                "mode": "probability",
                "max_length": 200,
                "probability": 0.7,
                "segment_delay": 1.0
            }
        },
        {
            "name": "禁用分段",
            "config": {
                "enabled": False
            }
        }
    ]
    
    for demo in demos:
        demo_mode(demo["name"], demo["config"])
        
        # 询问是否继续
        if demo != demos[-1]:  # 不是最后一个
            input("\n按回车键继续下一个演示...")
    
    print(f"\n{'='*60}")
    print("🎉 演示完成！")
    print("您可以根据需要修改 config.json 中的 message_segmentation 配置")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
