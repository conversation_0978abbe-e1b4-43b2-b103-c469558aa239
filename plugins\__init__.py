import os
import importlib
from typing import List, Type, Union
from core.plugin_base import Plugin
from core.startup_plugin import StartupPlugin

PLUGIN_DIR = os.path.dirname(__file__)

# 支持的插件基类
PLUGIN_BASE_CLASSES = (Plugin, StartupPlugin)


def discover_plugins() -> List[Type[Union[Plugin, StartupPlugin]]]:
    """
    发现 plugins 目录下所有有效的插件类
    """
    plugins = []

    for entry in os.listdir(PLUGIN_DIR):
        full_path = os.path.join(PLUGIN_DIR, entry)

        # 只处理目录且不是隐藏文件或特殊目录
        if os.path.isdir(full_path) and not entry.startswith('__') and not entry.startswith('.'):
            module_name = f"plugins.{entry}"
            try:
                plugin_module = importlib.import_module(module_name)

                # 检查模块中是否包含 PLUGIN_BASE_CLASSES 的子类
                for base_class in PLUGIN_BASE_CLASSES:
                    for attr_name in dir(plugin_module):
                        cls = getattr(plugin_module, attr_name)
                        if isinstance(cls, type) and issubclass(cls, base_class) and cls != base_class:
                            plugins.append(cls)
                            print(f"✅ 发现插件: {cls.__name__}")
            except Exception as e:
                print(f"❌ 加载插件失败 ({entry}): {e}")

    return plugins


def load_plugins(handler) -> List[Union[Plugin, StartupPlugin]]:
    """
    加载所有插件并返回插件实例列表
    """
    plugin_classes = discover_plugins()
    plugins = []

    for cls in plugin_classes:
        try:
            instance = cls(handler)
            plugins.append(instance)
            print(f"🔧 插件已加载: {cls.__name__}")
        except Exception as e:
            print(f"❌ 实例化插件失败: {cls.__name__}: {e}")

    # 保留原有优先级排序逻辑
    return sorted(plugins, key=lambda p: getattr(p, 'priority', 100))