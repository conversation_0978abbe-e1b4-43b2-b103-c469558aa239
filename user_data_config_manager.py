#!/usr/bin/env python3
"""
用户数据采集配置管理工具
用于管理微信用户数据采集的相关配置
"""

import os
import json
from datetime import datetime
from utils.config_utils import load_config
from db_plugins.mysql import MySQLDB


class UserDataConfigManager:
    """用户数据采集配置管理器"""
    
    def __init__(self):
        # 加载配置
        self.config = load_config()
        db_config = self.config.get("mysql", {})
        if not db_config:
            raise ValueError("❌ config.json 中未配置 mysql 数据库信息")
        
        # 连接数据库
        self.db = MySQLDB(**db_config)
        self.db.connect()
        
        # 注册SQL函数
        from plugins.user_info.sql import USER_INFO_SQL_FUNCTIONS
        self.db.register_plugin_functions("UserDataConfigManager", USER_INFO_SQL_FUNCTIONS)
        
        # 初始化表
        try:
            self.db.init_user_data_tables()
            print("✅ 数据库表初始化完成")
        except Exception as e:
            print(f"❌ 初始化数据库表失败: {e}")

    def show_current_config(self):
        """显示当前配置"""
        print("\n" + "="*50)
        print("📋 当前用户数据采集配置")
        print("="*50)
        
        try:
            config = self.db.get_user_data_config()
            if config:
                print(f"🔄 强制启动时采集: {'是' if config['force_collection_on_startup'] else '否'}")
                print(f"⏰ 采集间隔: {config['collection_interval_hours']} 小时")
                print(f"📅 最后采集时间: {config['last_collection_time'] or '未采集'}")
                print(f"📝 配置创建时间: {config['created_at']}")
                print(f"🔄 配置更新时间: {config['updated_at']}")
            else:
                print("⚠️ 未找到配置，将使用默认设置")
                
            # 显示程序状态
            last_status = self.db.get_last_program_status()
            if last_status:
                status, timestamp = last_status
                print(f"🔌 最后程序状态: {status} ({timestamp})")
            else:
                print("🔌 最后程序状态: 无记录")
                
        except Exception as e:
            print(f"❌ 获取配置失败: {e}")

    def set_force_collection(self, force: bool):
        """设置是否强制在启动时采集数据"""
        try:
            self.db.update_user_data_config(force_collection_on_startup=force)
            print(f"✅ 已设置强制启动采集为: {'是' if force else '否'}")
        except Exception as e:
            print(f"❌ 设置失败: {e}")

    def set_collection_interval(self, hours: int):
        """设置采集间隔（小时）"""
        if hours <= 0:
            print("❌ 采集间隔必须大于0小时")
            return
            
        try:
            self.db.update_user_data_config(collection_interval_hours=hours)
            print(f"✅ 已设置采集间隔为: {hours} 小时")
        except Exception as e:
            print(f"❌ 设置失败: {e}")

    def reset_last_collection_time(self):
        """重置最后采集时间（强制下次启动时采集）"""
        try:
            self.db.update_last_collection_time('1970-01-01 00:00:00')
            print("✅ 已重置最后采集时间，下次启动将强制采集数据")
        except Exception as e:
            print(f"❌ 重置失败: {e}")

    def show_program_status_log(self, limit: int = 10):
        """显示程序状态日志"""
        print(f"\n📊 最近 {limit} 条程序状态记录:")
        print("-" * 40)
        
        try:
            cursor = self.db.connection.cursor()
            cursor.execute("""
                SELECT status, timestamp 
                FROM program_status_log 
                ORDER BY timestamp DESC 
                LIMIT %s
            """, (limit,))
            
            records = cursor.fetchall()
            if records:
                for status, timestamp in records:
                    icon = "🟢" if status == "startup" else "🔴"
                    print(f"{icon} {status.upper()}: {timestamp}")
            else:
                print("📝 暂无记录")
                
        except Exception as e:
            print(f"❌ 获取状态日志失败: {e}")

    def interactive_menu(self):
        """交互式菜单"""
        while True:
            print("\n" + "="*50)
            print("🛠️  用户数据采集配置管理工具")
            print("="*50)
            print("1. 查看当前配置")
            print("2. 设置强制启动采集 (开启/关闭)")
            print("3. 设置采集间隔 (小时)")
            print("4. 重置最后采集时间")
            print("5. 查看程序状态日志")
            print("0. 退出")
            print("-" * 50)
            
            try:
                choice = input("请选择操作 (0-5): ").strip()
                
                if choice == "0":
                    print("👋 再见!")
                    break
                elif choice == "1":
                    self.show_current_config()
                elif choice == "2":
                    force_input = input("是否强制启动时采集? (y/n): ").strip().lower()
                    force = force_input in ['y', 'yes', '是', '1', 'true']
                    self.set_force_collection(force)
                elif choice == "3":
                    try:
                        hours = int(input("请输入采集间隔 (小时): ").strip())
                        self.set_collection_interval(hours)
                    except ValueError:
                        print("❌ 请输入有效的数字")
                elif choice == "4":
                    confirm = input("确认重置最后采集时间? (y/n): ").strip().lower()
                    if confirm in ['y', 'yes', '是', '1']:
                        self.reset_last_collection_time()
                elif choice == "5":
                    try:
                        limit = int(input("显示最近多少条记录? (默认10): ").strip() or "10")
                        self.show_program_status_log(limit)
                    except ValueError:
                        self.show_program_status_log()
                else:
                    print("❌ 无效选择，请重新输入")
                    
            except KeyboardInterrupt:
                print("\n👋 再见!")
                break
            except Exception as e:
                print(f"❌ 操作失败: {e}")

    def __del__(self):
        """清理资源"""
        if hasattr(self, 'db') and self.db:
            self.db.close()


def main():
    """主函数"""
    try:
        manager = UserDataConfigManager()
        manager.interactive_menu()
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")


if __name__ == "__main__":
    main()
