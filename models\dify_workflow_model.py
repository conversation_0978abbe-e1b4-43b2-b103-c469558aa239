import json
import requests
from typing import Dict, Any

from models import AIModel


class DifyWorkFlowModel(AIModel):
    def generate(self, context: str, prompt_template: str = None) -> str:
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        payload = {
            "inputs": {
                "astrbot_text_query": context
            },
            "response_mode": "streaming",
            "user": "terminal_user"
        }

        final_answer = ""
        try:
            with requests.post(self.url, headers=headers, json=payload, stream=True, timeout=60) as response:
                if response.status_code == 200:
                    for line in response.iter_lines():
                        if line:
                            decoded_line = line.decode('utf-8')
                            if decoded_line.startswith("data:"):
                                data_part = decoded_line[5:].strip()
                                try:
                                    json_data = json.loads(data_part)
                                    event = json_data.get("event")

                                    # 处理文本流（LLM 输出）
                                    if event == "message":
                                        chunk = json_data.get("answer", "")
                                        final_answer += chunk

                                    elif event == "message_end":
                                        break

                                    # 处理语音流（TTS 输出）
                                    elif event == "tts_message":
                                        chunk = json_data.get("audio", "")
                                        final_answer += "[音频]"
                                        print("[AUDIO]", end="", flush=True)

                                    elif event == "tts_message_end":
                                        break

                                    # 处理最终结果（适用于非流式输出）
                                    elif event == "workflow_finished":
                                        outputs = json_data.get("data", {}).get("outputs", {})
                                        if outputs:
                                            # 假设输出变量名为 astrbot_wf_output
                                            answer = outputs.get("astrbot_wf_output", "")
                                            final_answer += answer
                                        break

                                except json.JSONDecodeError:
                                    continue
                else:
                    error_text = response.text
                    return f"\n请求失败，状态码：{response.status_code}\n详细信息：{error_text}"
        except Exception as e:
            return f"\n发生错误：{e}"

        return final_answer.strip() or "\n未获得有效回复。请检查工作流输出节点是否配置了 astrbot_wf_output 字段，并确保输入被正确处理。"