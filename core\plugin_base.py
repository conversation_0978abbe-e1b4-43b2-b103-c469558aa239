from abc import ABC, abstractmethod
from typing import List, Optional

class Plugin(ABC):
    priority = 0  # 默认优先级，数字越小越先执行
    blacklist = []  # 默认为空列表

    def __init__(self, handler):
        self.handler = handler

    @abstractmethod
    def on_messages(self, chat: str, messages: List[str]) -> Optional[str]:
        """
        接收消息后调用，返回要发送的回复内容或 None 表示不处理
        :param chat: 聊天窗口名称
        :param messages: 收到的消息列表
        :return: 回复内容 或 None
        """
        pass