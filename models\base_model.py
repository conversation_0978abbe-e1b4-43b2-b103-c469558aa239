from abc import ABC, abstractmethod


class AIModel(ABC):
    def __init__(self, api_key: str, url: str, **kwargs):
        self.api_key = api_key
        self.url = url

    @abstractmethod
    def generate(self, context: str, prompt_template: str = None) -> str:
        """
        生成回复内容
        :param context: 用户输入上下文
        :param prompt_template: 自定义提示词模板（可选）
        :return: AI 回复内容
        """
        pass