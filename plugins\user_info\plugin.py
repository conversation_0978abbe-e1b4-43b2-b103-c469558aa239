import time
import threading
import atexit
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

# 插件基类
from core.startup_plugin import StartupPlugin

# 微信核心模块
from wxautox import WeChat

# 导入插件所需的SQL函数
from plugins.user_info.sql import USER_INFO_SQL_FUNCTIONS


class WeChatUserInfoPlugin(StartupPlugin):
    """
    微信用户信息采集插件：
    - 智能判断是否需要采集数据（基于配置和时间间隔）
    - 记录程序启动/停止状态
    - 支持用户配置强制采集或按时间间隔采集
    - 使用 handler.db 进行数据库操作
    """

    def __init__(self, handler):
        self.handler = handler
        self.wx = handler.wx  # 使用主处理程序中的 WeChat 实例

        # 使用主处理程序提供的数据库连接
        self.db = getattr(handler, 'db', None)
        if not self.db:
            raise ValueError("❌ handler 未绑定数据库实例，无法继续")

        # 注册本插件所需的 SQL 函数
        self.db.register_plugin_functions("WeChatUserInfoPlugin", USER_INFO_SQL_FUNCTIONS)

        # 初始化数据库表
        try:
            self.db.init_user_data_tables()
            self._log("✅ 用户数据相关表初始化完成")
        except Exception as e:
            self._log(f"❌ 初始化数据库表失败: {e}", level="ERROR")

        # 注册程序退出时的清理函数
        atexit.register(self._on_program_shutdown)

        super().__init__(handler)  # 调用父类初始化方法（会执行 initialize）

    def initialize(self):
        """初始化入口：智能判断是否需要采集数据"""
        # 记录程序启动状态
        try:
            self.db.record_program_status('startup')
            self._log("✅ 程序启动状态已记录")
        except Exception as e:
            self._log(f"❌ 记录程序启动状态失败: {e}", level="ERROR")

        # 智能判断是否需要采集数据
        if self._should_collect_data():
            self._log("【智能采集】开始采集微信用户信息...")
            try:
                self.collect_and_store_user_info()
                # 更新最后采集时间
                self.db.update_last_collection_time()
                self._log("✅ 数据采集完成，已更新最后采集时间")
            except Exception as e:
                self._log(f"❌ 数据采集失败: {e}", level="ERROR")
        else:
            self._log("⏭️ 根据配置和时间间隔，跳过本次数据采集")

        # 启动定时任务
        self._start_smart_collection_task()

    def on_messages(self, chat: str, messages: List[str]) -> Optional[str]:
        """
        消息处理接口（兼容性方法）
        用户信息采集插件不处理消息，直接返回None
        """
        return None

    def _should_collect_data(self) -> bool:
        """智能判断是否需要采集数据"""
        try:
            # 获取用户配置
            config = self.db.get_user_data_config()
            if not config:
                self._log("⚠️ 未找到用户数据配置，使用默认设置")
                return True  # 默认采集

            # 如果配置为强制采集，直接返回True
            if config['force_collection_on_startup']:
                self._log("🔄 配置为强制采集，将执行数据采集")
                return True

            # 检查时间间隔
            last_collection_time = config['last_collection_time']
            if not last_collection_time:
                self._log("📅 首次运行，将执行数据采集")
                return True

            # 计算时间差
            now = datetime.now()
            time_diff = now - last_collection_time
            interval_hours = config['collection_interval_hours']

            if time_diff.total_seconds() >= interval_hours * 3600:
                self._log(f"⏰ 距离上次采集已过 {time_diff} (设定间隔: {interval_hours}小时)，将执行数据采集")
                return True
            else:
                remaining_time = timedelta(hours=interval_hours) - time_diff
                self._log(f"⏳ 距离上次采集仅过 {time_diff}，还需等待 {remaining_time} 才会采集")
                return False

        except Exception as e:
            self._log(f"❌ 判断是否需要采集数据时出错: {e}", level="ERROR")
            return True  # 出错时默认采集

    def _on_program_shutdown(self):
        """程序退出时的清理函数"""
        try:
            self.db.record_program_status('shutdown')
            self._log("✅ 程序停止状态已记录")
        except Exception as e:
            self._log(f"❌ 记录程序停止状态失败: {e}", level="ERROR")

    def _start_smart_collection_task(self):
        """启动智能定时采集的后台线程"""
        def run():
            while True:
                try:
                    # 获取配置
                    config = self.db.get_user_data_config()
                    if not config:
                        interval_hours = 24  # 默认24小时
                    else:
                        interval_hours = config['collection_interval_hours']

                    # 计算下次执行时间
                    wait_seconds = interval_hours * 3600
                    next_run_time = datetime.now() + timedelta(seconds=wait_seconds)

                    self._log(f"⏳ 下次智能采集将在 {next_run_time} 执行，等待 {interval_hours} 小时")
                    time.sleep(wait_seconds)

                    # 执行采集
                    if self._should_collect_data():
                        self._log("⏰ 开始定时智能采集微信用户信息...")
                        try:
                            self.collect_and_store_user_info()
                            self.db.update_last_collection_time()
                            self._log("✅ 定时采集完成，已更新最后采集时间")
                        except Exception as e:
                            self._log(f"❌ 定时采集失败: {e}", level="ERROR")
                    else:
                        self._log("⏭️ 定时检查：根据配置跳过本次采集")

                except Exception as e:
                    self._log(f"❌ 定时任务执行失败: {e}", level="ERROR")
                    time.sleep(3600)  # 出错时等待1小时后重试

        thread = threading.Thread(target=run, daemon=True)
        thread.start()
        self._log("✅ 已启动智能定时采集任务")

    def set_collection_config(self, force_on_startup: bool = None, interval_hours: int = None):
        """设置数据采集配置

        Args:
            force_on_startup: 是否强制在启动时采集数据
            interval_hours: 数据采集间隔（小时）
        """
        try:
            self.db.update_user_data_config(force_on_startup, interval_hours)
            self._log(f"✅ 配置已更新 - 强制启动采集: {force_on_startup}, 采集间隔: {interval_hours}小时")
        except Exception as e:
            self._log(f"❌ 更新配置失败: {e}", level="ERROR")

    def get_collection_status(self):
        """获取数据采集状态信息"""
        try:
            config = self.db.get_user_data_config()
            last_status = self.db.get_last_program_status()

            status_info = {
                'config': config,
                'last_program_status': last_status,
                'next_collection_time': None
            }

            if config and config['last_collection_time']:
                next_time = config['last_collection_time'] + timedelta(hours=config['collection_interval_hours'])
                status_info['next_collection_time'] = next_time

            return status_info
        except Exception as e:
            self._log(f"❌ 获取采集状态失败: {e}", level="ERROR")
            return None

    def collect_and_store_user_info(self):
        """采集并存储所有相关信息"""
        try:
            self._collect_groups()
            self._collect_friends()
        finally:
            # 最终切回主窗口，保证监听正常
            try:
                self.wx.ChatWith('黄斌')  # 切回主窗口
            except Exception as e:
                self._log(f"⚠️ 切回主窗口失败: {e}", level="WARNING")

    def _collect_groups(self):
        """采集群聊信息并写入数据库"""
        groups_list = self.wx.GetAllRecentGroups()  # [('工作群', '500'), ...]
        if not groups_list:
            self._log("⚠️ 没有获取到群聊信息")
            return

        for group_info in groups_list:
            group_name, member_count = group_info
            try:
                member_count = int(member_count)
            except (ValueError, TypeError):
                member_count = 0

            try:
                self.db.insert_or_update_group(group_name, member_count)
                self._log(f"✅ 群聊信息已写入：{group_name}（成员数：{member_count}）")
            except Exception as e:
                self._log(f"❌ 写入群聊信息失败: {e}", level="ERROR")

    def _collect_friends(self):
        """采集好友和企业微信联系人信息并写入数据库"""
        friends_details = self.wx.GetFriendDetails()
        if not friends_details:
            self._log("⚠️ 没有获取到好友信息")
            return

        for detail in friends_details:
            if not detail:
                continue

            is_work_contact = '企业' in detail

            if is_work_contact:
                self._handle_company_friend(detail)
            else:
                self._handle_personal_friend(detail)

    def _handle_company_friend(self, detail: dict):
        """处理企业微信联系人"""
        company = detail.get('企业')
        nickname = detail.get('昵称')
        true_name = detail.get('实名')
        phone = detail.get('电话')
        remark = detail.get('备注')
        common_groups = detail.get('共同群聊')

        try:
            self.db.insert_or_update_company_friend(
                company, nickname, true_name, phone, remark, common_groups
            )
            self._log(f"✅ 企业微信：{nickname} 已写入数据库")
        except Exception as e:
            self._log(f"❌ 写入企业微信失败: {e}", level="ERROR")

        # 处理系统标签
        tags = detail.get('标签', [])
        if isinstance(tags, str):
            tags = [tags]
        for tag in tags:
            try:
                self.db.insert_company_tag(company, nickname, tag.strip())
            except Exception as e:
                self._log(f"❌ 写入企业微信标签失败: {e}", level="ERROR")

    def _handle_personal_friend(self, detail: dict):
        """处理个人微信好友"""
        wxid = detail.get('微信号')
        if not wxid:
            return

        nickname = detail.get('昵称')
        remark = detail.get('备注')
        region = detail.get('地区')
        phone = detail.get('电话')
        signature = detail.get('个性签名')
        common_groups = detail.get('共同群聊')
        source = detail.get('来源')

        try:
            self.db.insert_or_update_personal_friend(
                wxid, nickname, remark, region, phone, signature, common_groups, source
            )
            self._log(f"✅ 个人微信：{nickname} 已写入数据库")
        except Exception as e:
            self._log(f"❌ 写入个人微信失败: {e}", level="ERROR")

        # 处理系统标签
        tags = detail.get('标签', [])
        if isinstance(tags, str):
            tags = [tags]
        for tag in tags:
            try:
                self.db.insert_personal_tag(wxid, tag.strip())
            except Exception as e:
                self._log(f"❌ 写入个人标签失败: {e}", level="ERROR")

    def _log(self, msg: str, level: str = "INFO"):
        print(f"[微信用户信息采集插件] [{level}] {msg}")