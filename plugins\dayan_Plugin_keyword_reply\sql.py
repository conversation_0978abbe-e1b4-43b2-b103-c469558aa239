"""
Keyword Reply Plugin SQL Functions
包含关键词回复插件所需的所有SQL查询函数
"""
from typing import List, Dict


def get_all_keyword_rules(self) -> List[Dict]:
    """获取普通关键词回复插件的规则（keyword_rules + keyword_rules_collection）"""
    cursor = self.connection.cursor(dictionary=True)
    cursor.execute("""
        SELECT kr.id, kr.chat_name, kr.chat_type, kr.enabled, 
               kr.use_global_rules, kr.global_rule, krc.user_keyword, krc.default_reply
        FROM keyword_rules kr
        LEFT JOIN keyword_rules_collection krc ON kr.id = krc.keyword_id
    """)
    return cursor.fetchall()


# 导出所有SQL函数
KEYWORD_REPLY_SQL_FUNCTIONS = {
    'get_all_keyword_rules': get_all_keyword_rules,
}
