import os
import json
import requests
from typing import List, Optional
from . import Plugin


# 加载关键词配置
KEYWORDS_PATH = os.path.join(os.path.dirname(__file__), "config", "ai_keywords.json")
with open(KEYWORDS_PATH, "r", encoding="utf-8") as f:
    KEYWORDS_CONFIG = json.load(f)

# 加载 AI 智能体配置
AGENTS_PATH = os.path.join(os.path.dirname(__file__), "config", "ai_agents.json")
with open(AGENTS_PATH, "r", encoding="utf-8") as f:
    AGENTS_CONFIG = json.load(f)


def get_global_keyword_config():
    """获取全局关键词设置"""
    return KEYWORDS_CONFIG.get("全局设置", {})


def get_chat_config(chat_name: str) -> dict:
    """获取指定聊天窗口的关键词配置"""
    return KEYWORDS_CONFIG.get(chat_name, {})


def is_chat_enabled(chat_name: str) -> bool:
    """判断当前聊天是否启用了 AI 回复"""
    chat_config = get_chat_config(chat_name)
    if "启用" in chat_config:
        return chat_config["启用"]
    return True  # 默认启用


def get_trigger_keywords(chat_name: str) -> list:
    """获取当前聊天使用的关键词列表"""
    chat_config = get_chat_config(chat_name)
    if "关键词" in chat_config and chat_config.get("启用", True):
        return chat_config["关键词"]

    global_config = get_global_keyword_config()
    if global_config.get("启用关键词触发", True):
        return global_config.get("全局触发词", [])
    return []  # 如果全局关键词也关闭，则不匹配任何内容


def is_message_match_keyword(message: str, keywords: list) -> bool:
    """判断消息是否包含任意一个关键词"""
    return any(keyword in message for keyword in keywords)


def get_agent_config(chat_name: str):
    """获取指定聊天窗口对应的 AI 智能体配置"""
    return AGENTS_CONFIG.get(chat_name, AGENTS_CONFIG.get("默认"))


def get_ai_reply(context: str, agent_config: dict) -> str:
    api_key = agent_config["api_key"]
    url = agent_config["url"]

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    payload = {
        "inputs": {},
        "query": context,
        "response_mode": "streaming",
        "user": "terminal_user"
    }

    final_answer = ""
    try:
        with requests.post(url, headers=headers, json=payload, stream=True, timeout=60) as response:
            if response.status_code == 200:
                for line in response.iter_lines():
                    if line:
                        decoded_line = line.decode('utf-8')
                        if decoded_line.startswith("data:"):
                            try:
                                json_data = json.loads(decoded_line[5:].strip())
                                if json_data.get("event") == "message" and "answer" in json_data:
                                    chunk = json_data["answer"]
                                    final_answer += chunk
                            except Exception as e:
                                continue
            else:
                return "\nAI接口请求失败，请检查网络或API状态。"
    except Exception as e:
        return f"\n发生错误：{e}"

    return final_answer


class AIChatPlugin(Plugin):
    """
    AI聊天插件：支持全局关键词 + 群聊/好友专属关键词 + 启用/禁用开关
    默认优先级：150（在关键词等插件之后）
    """

    priority = 150  # 在关键词等插件之后触发

    def __init__(self, handler):
        super().__init__(handler)
        self.context_cache = {}  # 可选：缓存上下文用于多轮对话记忆

    def on_messages(self, chat: str, messages: List[str]) -> Optional[str]:
        self.handler._log(f"[AIChatPlugin] 正在处理来自 '{chat}' 的 {len(messages)} 条消息")

        if not messages:
            return None

        # 判断当前聊天是否被禁用
        if not is_chat_enabled(chat):
            self.handler._log(f"[AIChatPlugin] 聊天 '{chat}' 已被禁用，跳过 AI 处理")
            return None

        # 获取关键词
        trigger_keywords = get_trigger_keywords(chat)

        # 如果没有关键词，说明关键词触发机制已关闭（全局或局部）
        if not trigger_keywords:
            self.handler._log(f"[AIChatPlugin] 关键词触发已关闭，自动回复所有消息（来自 '{chat}'）")
            return self.generate_ai_reply(chat, messages)

        # 判断是否有消息匹配关键词
        if not any(is_message_match_keyword(msg, trigger_keywords) for msg in messages):
            self.handler._log(f"[AIChatPlugin] 当前聊天 '{chat}' 已启用AI关键词触发，但消息未匹配到关键词：{trigger_keywords}")
            return None

        # 触发 AI 回复
        return self.generate_ai_reply(chat, messages)

    def generate_ai_reply(self, chat: str, messages: List[str]) -> str:
        """生成 AI 回复内容（使用真实的 API 请求）"""

        # 获取当前聊天对应的 AI 智能体配置
        agent_config = get_agent_config(chat)
        if not agent_config:
            self.handler._log(f"[AIChatPlugin] 没有为 '{chat}' 配置智能体，默认使用‘默认’智能体")
            agent_config = get_agent_config("默认")

        # 拼接完整上下文
        full_context = "\n".join([f"用户：{msg}" for msg in messages])
        prompt = (
            "你是一个智能助手，以下是用户连续发送的消息，请综合理解上下文并给出合理回答：\n\n"
            + full_context
        )

        # 调用 AI 回复
        ai_response = get_ai_reply(prompt, agent_config)

        if ai_response:
            self.handler._log(f"[AIChatPlugin] 已生成 AI 回复：{ai_response[:30]}...")
            return ai_response

        return "抱歉，我现在无法提供帮助，请稍后再试。"