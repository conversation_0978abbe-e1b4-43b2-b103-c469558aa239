#!/usr/bin/env python3
"""
测试定时消息插件功能
"""

import os
import sys
from datetime import datetime, time, timedelta
from utils.config_utils import load_config
from db_plugins.mysql import MySQLDB


def test_scheduled_message_plugin():
    """测试定时消息插件功能"""
    print("🧪 开始测试定时消息插件...")
    
    try:
        # 加载配置
        cfg = load_config()
        db_config = cfg.get("mysql", {})
        if not db_config:
            print("❌ 未找到数据库配置")
            return False
        
        # 连接数据库
        db = MySQLDB(**db_config)
        db.connect()
        
        # 注册SQL函数
        from plugins.scheduled_message.sql import SCHEDULED_MESSAGE_SQL_FUNCTIONS
        db.register_plugin_functions("TestScheduledMessage", SCHEDULED_MESSAGE_SQL_FUNCTIONS)
        
        # 测试初始化表
        print("📋 测试初始化数据库表...")
        db.init_scheduled_message_tables()
        print("✅ 数据库表初始化成功")
        
        # 测试添加每日任务
        print("\n📅 测试添加每日定时任务...")
        success = db.add_scheduled_message(
            task_name="测试每日任务",
            chat_name="测试群",
            chat_type="group",
            message_content="这是一条测试消息",
            schedule_type="daily",
            schedule_time=time(9, 30),
            max_send_count=5
        )
        if success:
            print("✅ 每日任务添加成功")
        else:
            print("❌ 每日任务添加失败")
        
        # 测试添加间隔任务
        print("\n⏰ 测试添加间隔定时任务...")
        success = db.add_scheduled_message(
            task_name="测试间隔任务",
            chat_name="测试私聊",
            chat_type="private",
            message_content="间隔测试消息",
            schedule_type="interval",
            interval_minutes=30,
            max_send_count=3
        )
        if success:
            print("✅ 间隔任务添加成功")
        else:
            print("❌ 间隔任务添加失败")
        
        # 测试添加一次性任务
        print("\n📆 测试添加一次性定时任务...")
        future_time = datetime.now() + timedelta(minutes=5)
        success = db.add_scheduled_message(
            task_name="测试一次性任务",
            chat_name="测试群",
            chat_type="group",
            message_content="一次性测试消息",
            schedule_type="once",
            schedule_date=future_time.date(),
            schedule_time=future_time.time(),
            max_send_count=1
        )
        if success:
            print("✅ 一次性任务添加成功")
        else:
            print("❌ 一次性任务添加失败")
        
        # 测试获取所有任务
        print("\n📋 测试获取所有定时任务...")
        tasks = db.get_all_scheduled_messages()
        print(f"✅ 找到 {len(tasks)} 个定时任务:")
        for task in tasks:
            print(f"  - {task['task_name']} ({task['schedule_type']}) -> {task['chat_name']}")
        
        # 测试获取待发送消息
        print("\n📤 测试获取待发送消息...")
        pending = db.get_pending_messages()
        print(f"✅ 找到 {len(pending)} 条待发送消息:")
        for msg in pending:
            print(f"  - {msg['task_name']} -> {msg['chat_name']} (下次发送: {msg['next_send_at']})")
        
        # 测试更新任务
        print("\n🔄 测试更新任务...")
        success = db.update_scheduled_message("测试每日任务", enabled=False)
        if success:
            print("✅ 任务更新成功")
        else:
            print("❌ 任务更新失败")
        
        # 测试查询特定任务
        print("\n🔍 测试查询特定任务...")
        task = db.get_scheduled_message_by_name("测试每日任务")
        if task:
            print(f"✅ 找到任务: {task['task_name']} (启用状态: {task['enabled']})")
        else:
            print("❌ 未找到任务")
        
        # 测试发送历史
        print("\n📊 测试发送历史...")
        history = db.get_message_history(limit=10)
        print(f"✅ 找到 {len(history)} 条历史记录")
        
        # 清理测试数据
        print("\n🧹 清理测试数据...")
        db.delete_scheduled_message("测试每日任务")
        db.delete_scheduled_message("测试间隔任务")
        db.delete_scheduled_message("测试一次性任务")
        print("✅ 测试数据清理完成")
        
        # 关闭数据库连接
        db.close()
        
        print("\n✅ 定时消息插件测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_plugin_loading():
    """测试插件加载"""
    print("\n🔍 测试插件加载...")
    
    try:
        from plugins.scheduled_message.plugin import ScheduledMessagePlugin
        from plugins.scheduled_message.sql import SCHEDULED_MESSAGE_SQL_FUNCTIONS
        
        print(f"✅ ScheduledMessagePlugin 类加载成功")
        print(f"  - 优先级: {getattr(ScheduledMessagePlugin, 'priority', 100)}")
        print(f"  - SQL函数数量: {len(SCHEDULED_MESSAGE_SQL_FUNCTIONS)}")
        print(f"  - SQL函数列表: {list(SCHEDULED_MESSAGE_SQL_FUNCTIONS.keys())}")
        
        # 检查必要的方法
        required_methods = [
            'on_messages', '_scheduler_loop', '_check_and_send_messages',
            'add_daily_message', 'add_weekly_message', 'add_interval_message',
            'add_once_message', 'remove_scheduled_message', 'enable_scheduled_message'
        ]
        
        for method in required_methods:
            if hasattr(ScheduledMessagePlugin, method):
                print(f"  ✅ 方法存在: {method}")
            else:
                print(f"  ❌ 方法缺失: {method}")
        
        return True
        
    except Exception as e:
        print(f"❌ 插件加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_time_calculation():
    """测试时间计算功能"""
    print("\n🕐 测试时间计算功能...")
    
    try:
        from plugins.scheduled_message.sql import calculate_next_send_time
        from datetime import time, date
        
        # 测试每日调度
        print("📅 测试每日调度时间计算...")
        next_time = calculate_next_send_time(
            schedule_type="daily",
            schedule_time=time(9, 30)
        )
        print(f"  下次发送时间: {next_time}")
        
        # 测试间隔调度
        print("⏰ 测试间隔调度时间计算...")
        next_time = calculate_next_send_time(
            schedule_type="interval",
            interval_minutes=60
        )
        print(f"  下次发送时间: {next_time}")
        
        # 测试一次性调度
        print("📆 测试一次性调度时间计算...")
        future_date = date.today() + timedelta(days=1)
        next_time = calculate_next_send_time(
            schedule_type="once",
            schedule_date=future_date,
            schedule_time=time(14, 30)
        )
        print(f"  下次发送时间: {next_time}")
        
        print("✅ 时间计算功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 时间计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🧪 定时消息插件测试工具")
    print("=" * 50)
    
    # 测试插件加载
    test_plugin_loading()
    
    # 测试时间计算
    test_time_calculation()
    
    # 测试数据库功能
    test_scheduled_message_plugin()
    
    print("\n✅ 所有测试完成")
