import os
import json
from typing import List, Optional, Dict, Any

# 抽象基类 Plugin 和 MySQLDB 数据库操作类
from core.plugin_base import Plugin
from db_plugins.mysql import MySQLDB  # 假设你有一个 MySQLDB 类来处理数据库操作
# 模型基类和具体模型实现
from models import (
    AIModel,
    GPT35TurboModel,
    DifyChatFlowModel,
    DifyWorkFlowModel,
    DifyAgentModel
)
# 导入插件的SQL函数
from .sql import AI_CHAT_SQL_FUNCTIONS


class AIChatPlugin(Plugin):
    MODEL_MAP = {
        "gpt-3.5-turbo": GPT35TurboModel,
        "dify_chatflow": DifyChatFlowModel,
        "dify_workflow": DifyWorkFlowModel,
        "dify_agent": DifyAgentModel
    }

    priority = 150

    def __init__(self, handler):
        super().__init__(handler)

        # 加载数据库配置文件路径
        # 使用主处理程序提供的数据库连接
        self.db = getattr(handler, 'db', None)
        if not self.db:
            raise ValueError("❌ handler 未绑定数据库实例，无法继续")

        # 注册本插件所需的 SQL 函数
        self.db.register_plugin_functions("AIChatPlugin", AI_CHAT_SQL_FUNCTIONS)

        # 缓存规则数据
        self.cache = self._load_all_rules_from_db()
        self.context_cache = {}  # 可用于缓存多轮对话

    def __del__(self):
        """插件析构时卸载SQL函数"""
        if hasattr(self, 'db') and self.db:
            self.db.unregister_plugin_functions("AIChatPlugin")

    def _load_all_rules_from_db(self) -> Dict[str, Any]:
        results = self.db.get_all_ai_keyword_rules_with_agent()

        cache = {
            "default_group_keywords": [],
            "default_private_keywords": [],
            "chat_specific_keywords": {}
        }

        chat_data = {}

        for row in results:
            chat_name = row["chat_name"]
            chat_type = row["chat_type"]
            global_rule = row["global_rule"]
            keyword = row["user_keyword"]
            agent_id = row["agent_id"]
            reply_prompt = row["reply_prompt"]

            if not keyword or not agent_id:
                continue

            if chat_name not in chat_data:
                chat_data[chat_name] = {
                    "type": chat_type,
                    "keywords": []
                }

            chat_data[chat_name]["keywords"].append((keyword, agent_id, reply_prompt))

        for chat_name, data in chat_data.items():
            if data.pop("global_rule", False):
                if data["type"] == "group":
                    cache["default_group_keywords"].extend(data["keywords"])
                else:
                    cache["default_private_keywords"].extend(data["keywords"])
            else:
                cache["chat_specific_keywords"][chat_name] = data

        return cache

    def on_messages(self, chat: str, messages: List[str]) -> Optional[str]:
        chat_config = self.cache["chat_specific_keywords"].get(chat, {})
        chat_type = chat_config.get("type", "group")
        chat_enabled = chat_config.get("enabled", True)

        if not chat_enabled:
            return None

        matched_keyword = None
        matched_agent_id = None
        matched_prompt = None

        local_keywords = chat_config.get("keywords", [])
        # 匹配局部关键词规则
        for msg in reversed(messages):
            for keyword, agent_id, reply_prompt in local_keywords:
                if keyword in msg:
                    matched_keyword = keyword
                    matched_agent_id = agent_id
                    matched_prompt = reply_prompt
                    break
            if matched_keyword:
                break

        if not matched_keyword:
            # 尝试全局关键词
            keywords_list = self.cache[
                "default_group_keywords" if chat_type == "group" else "default_private_keywords"
            ]
            for msg in reversed(messages):
                for keyword, agent_id, reply_prompt in keywords_list:
                    if keyword in msg:
                        matched_keyword = keyword
                        matched_agent_id = agent_id
                        matched_prompt = reply_prompt
                        break
                if matched_keyword:
                    break


        if matched_keyword and matched_agent_id:
            agent_config = self._get_agent_config_by_id(matched_agent_id)
            if agent_config:
                model_type = agent_config.get("model_type", "gpt-3.5-turbo")
                model_class = self.MODEL_MAP.get(model_type)

                if not model_class:
                    self.handler._log(f"[AIChatPlugin] 不支持的模型类型: {model_type}")
                    return "抱歉，不支持该模型类型。"

                # 实例化模型
                model_kwargs = {
                    "api_key": agent_config["api_key"],
                    "url": agent_config["url"]
                }

                ai_model: AIModel = model_class(**model_kwargs)
                ai_response = ai_model.generate(
                    context=self._build_context(messages),
                    prompt_template=matched_prompt
                )

                if ai_response:
                    self.handler._log(f"[AIChatPlugin] 【{chat_type}】已生成 AI 回复：{ai_response[:30]}...")
                    return ai_response

        return None

    def _get_agent_config_by_id(self, agent_id: int) -> Optional[Dict]:
        return self.db.get_ai_agent_by_id(agent_id)

    def _build_context(self, messages: List[str]) -> str:
        return "\n".join([f"用户：{msg}" for msg in messages])