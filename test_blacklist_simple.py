#!/usr/bin/env python3
"""
简单测试黑名单插件加载和优先级
"""

import os
import sys


def test_plugin_loading():
    """测试插件加载和优先级"""
    print("🧪 测试插件加载和优先级...")
    
    try:
        from plugins import discover_plugins, load_plugins
        
        # 发现插件
        plugin_classes = discover_plugins()
        print(f"✅ 发现 {len(plugin_classes)} 个插件类:")
        
        blacklist_plugins = []
        for cls in plugin_classes:
            print(f"  - {cls.__name__} (优先级: {getattr(cls, 'priority', 100)})")
            if 'blacklist' in cls.__name__.lower():
                blacklist_plugins.append(cls)
        
        print(f"\n🚫 发现 {len(blacklist_plugins)} 个黑名单相关插件:")
        for cls in blacklist_plugins:
            print(f"  - {cls.__name__} (优先级: {getattr(cls, 'priority', 100)})")
        
        # 模拟handler对象
        class MockHandler:
            def __init__(self):
                self.db = None
            
            def _log(self, msg, level='INFO'):
                print(f"[{level}] {msg}")
        
        mock_handler = MockHandler()
        
        # 尝试加载插件（不包括需要数据库的插件）
        print(f"\n🔧 测试插件实例化...")
        loaded_plugins = []
        
        for cls in plugin_classes:
            try:
                # 跳过需要数据库的插件
                if cls.__name__ in ['BlacklistPlugin', 'KeywordReplyPlugin', 'AIChatPlugin', 'ConversationHistoryPlugin', 'WeChatUserInfoPlugin']:
                    print(f"⏭️ 跳过需要数据库的插件: {cls.__name__}")
                    continue
                
                instance = cls(mock_handler)
                loaded_plugins.append(instance)
                print(f"✅ 成功加载: {cls.__name__}")
            except Exception as e:
                print(f"❌ 加载失败: {cls.__name__}: {e}")
        
        # 按优先级排序
        loaded_plugins.sort(key=lambda p: getattr(p, 'priority', 100))
        
        print(f"\n📋 插件加载顺序 (按优先级):")
        for i, plugin in enumerate(loaded_plugins):
            print(f"  {i+1}. {plugin.__class__.__name__} (优先级: {getattr(plugin, 'priority', 100)})")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_blacklist_plugin_structure():
    """测试黑名单插件结构"""
    print("\n🔍 测试黑名单插件结构...")
    
    try:
        from plugins.blacklist_plugin.plugin import BlacklistPlugin
        from plugins.blacklist_plugin.sql import BLACKLIST_SQL_FUNCTIONS
        
        print(f"✅ BlacklistPlugin 类加载成功")
        print(f"  - 优先级: {getattr(BlacklistPlugin, 'priority', 100)}")
        print(f"  - SQL函数数量: {len(BLACKLIST_SQL_FUNCTIONS)}")
        print(f"  - SQL函数列表: {list(BLACKLIST_SQL_FUNCTIONS.keys())}")
        
        # 检查必要的方法
        required_methods = ['on_messages', '_load_blacklist_from_db', 'add_to_blacklist', 'remove_from_blacklist']
        for method in required_methods:
            if hasattr(BlacklistPlugin, method):
                print(f"  ✅ 方法存在: {method}")
            else:
                print(f"  ❌ 方法缺失: {method}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_old_blacklist_plugin():
    """检查旧的黑名单插件"""
    print("\n🔍 检查旧的黑名单插件...")
    
    try:
        from plugins.nonono import OldBlacklistPlugin
        
        print(f"✅ OldBlacklistPlugin 类加载成功")
        print(f"  - 优先级: {getattr(OldBlacklistPlugin, 'priority', 100)}")
        print(f"  - 硬编码黑名单: {getattr(OldBlacklistPlugin, 'blacklist', [])}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🧪 黑名单插件简单测试工具")
    print("=" * 50)
    
    # 测试插件加载
    test_plugin_loading()
    
    # 测试黑名单插件结构
    test_blacklist_plugin_structure()
    
    # 检查旧插件
    check_old_blacklist_plugin()
    
    print("\n✅ 测试完成")
