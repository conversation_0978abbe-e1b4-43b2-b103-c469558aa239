import openai
from typing import Optional

from models import AIModel


class GPT35TurboModel(AIModel):
    def __init__(self, api_key: str, url: str = None):
        """
        初始化 GPT-3.5-Turbo 模型
        :param api_key: API Key
        :param url: 自定义 API 基础地址（如代理服务）
        """
        super().__init__(api_key, url)
        self.api_key = api_key
        self.url = url

        # 设置 OpenAI SDK 的 base_url
        openai.api_key = self.api_key
        if self.url:
            # 确保 base_url 以 '/' 结尾，避免出现 /v1chat/completions 这类错误
            openai.base_url = self.url.rstrip("/") + "/"

    def generate(self, context: str, prompt_template: Optional[str] = None) -> str:
        """
        调用 GPT-3.5-Turbo 模型生成回复
        :param context: 用户输入上下文
        :param prompt_template: 自定义提示词模板（可选）
        :return: AI 回复内容
        """

        # 构建最终提示词
        if prompt_template:
            if "{context}" in prompt_template:
                final_prompt = prompt_template.format(context=context)
            else:
                final_prompt = f"{prompt_template}\n\n{context}"
        else:
            final_prompt = context

        try:
            # 调用 OpenAI 接口
            completion = openai.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "user", "content": final_prompt}
                ],
                temperature=0.7,
                max_tokens=2048
            )

            return completion.choices[0].message.content.strip()

        except Exception as e:
            return f"\n[ERROR] GPT-3.5-Turbo 接口异常: {str(e)}" \
                   f"\nURL: {openai.base_url}" \
                   f"\nAPI_KEY: {self.api_key[:6]}...{self.api_key[-4:]}"