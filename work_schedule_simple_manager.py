#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版工作时间管理工具
用于管理存储在数据库中的人工服务工作时间配置
"""

import sys
import json
from datetime import datetime

def show_usage():
    """显示使用说明"""
    print("工作时间数据库管理工具（简化版）")
    print("=" * 50)
    print("说明：人工服务的工作时间配置现已存储在数据库中")
    print()
    print("📋 配置项说明:")
    print("  - enabled: 是否启用工作时间功能")
    print("  - work_days: 工作日设置（1=周一，7=周日）")
    print("  - work_start: 工作开始时间（HH:MM格式）")
    print("  - work_end: 工作结束时间（HH:MM格式）")
    print("  - timezone: 时区设置")
    print("  - holiday_mode: 节假日模式")
    print("  - work_hours_response: 工作时间响应消息")
    print("  - non_work_hours_response: 非工作时间响应消息")
    print("  - weekend_response: 周末时间响应消息")
    print("  - holiday_response: 节假日响应消息")
    print()
    print("🗄️ 数据库表结构:")
    print("  表名: work_schedule_config")
    print("  字段: config_key, config_value, config_type, description")
    print()
    print("📝 默认配置:")
    print("  工作日: 周一到周五")
    print("  工作时间: 09:00-18:00")
    print("  节假日模式: 关闭")
    print()
    print("🔧 管理方式:")
    print("  1. 直接在数据库中修改 work_schedule_config 表")
    print("  2. 使用机器人程序的管理界面（如果有）")
    print("  3. 通过插件的API接口修改")
    print()
    print("⚠️ 注意事项:")
    print("  - 修改配置后需要重启机器人程序")
    print("  - config_type 字段指定值的类型（string/boolean/json/integer）")
    print("  - work_days 使用JSON格式存储数组")
    print()
    print("📊 配置示例:")
    
    examples = [
        {
            "config_key": "enabled",
            "config_value": "true",
            "config_type": "boolean",
            "description": "是否启用工作时间功能"
        },
        {
            "config_key": "work_days",
            "config_value": "[1,2,3,4,5]",
            "config_type": "json",
            "description": "工作日设置（1=周一，7=周日）"
        },
        {
            "config_key": "work_start",
            "config_value": "09:00",
            "config_type": "string",
            "description": "工作开始时间"
        },
        {
            "config_key": "work_end",
            "config_value": "18:00",
            "config_type": "string",
            "description": "工作结束时间"
        },
        {
            "config_key": "holiday_mode",
            "config_value": "false",
            "config_type": "boolean",
            "description": "节假日模式"
        }
    ]
    
    for example in examples:
        print(f"  {example['config_key']}: {example['config_value']} ({example['config_type']})")
    
    print()
    print("🚀 快速设置SQL:")
    print("-- 设置工作时间为 8:30-17:30")
    print("UPDATE work_schedule_config SET config_value='08:30' WHERE config_key='work_start';")
    print("UPDATE work_schedule_config SET config_value='17:30' WHERE config_key='work_end';")
    print()
    print("-- 设置工作日为周一到周六")
    print("UPDATE work_schedule_config SET config_value='[1,2,3,4,5,6]' WHERE config_key='work_days';")
    print()
    print("-- 启用节假日模式")
    print("UPDATE work_schedule_config SET config_value='true' WHERE config_key='holiday_mode';")
    print()
    print("-- 自定义工作时间响应消息")
    print("UPDATE work_schedule_config SET config_value='客服在线，马上为您处理！' WHERE config_key='work_hours_response';")

def show_current_time_status():
    """显示当前时间状态"""
    print("🕐 当前时间状态分析")
    print("=" * 50)
    
    now = datetime.now()
    weekday = now.isoweekday()
    current_time = now.time()
    
    print(f"当前时间: {now.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"星期: 周{weekday} ({'周一' if weekday == 1 else '周二' if weekday == 2 else '周三' if weekday == 3 else '周四' if weekday == 4 else '周五' if weekday == 5 else '周六' if weekday == 6 else '周日'})")
    
    # 默认配置分析
    default_work_days = [1, 2, 3, 4, 5]  # 周一到周五
    default_work_start = "09:00"
    default_work_end = "18:00"
    
    print(f"\n基于默认配置的分析:")
    print(f"默认工作日: 周一到周五")
    print(f"默认工作时间: {default_work_start}-{default_work_end}")
    
    if weekday not in default_work_days:
        status = "周末时间"
        message_type = "weekend"
    else:
        start_hour, start_minute = map(int, default_work_start.split(':'))
        end_hour, end_minute = map(int, default_work_end.split(':'))
        
        if (current_time.hour > start_hour or (current_time.hour == start_hour and current_time.minute >= start_minute)) and \
           (current_time.hour < end_hour or (current_time.hour == end_hour and current_time.minute <= end_minute)):
            status = "工作时间"
            message_type = "work_hours"
        else:
            status = "非工作时间"
            message_type = "non_work_hours"
    
    print(f"当前状态: {status}")
    print(f"消息类型: {message_type}")
    
    # 显示对应的响应消息
    default_responses = {
        "work_hours": "您好！我已为您转接人工客服，客服人员会尽快为您处理。",
        "non_work_hours": "您好！现在是非工作时间（工作时间：周一至周五 09:00-18:00），您的问题已记录，客服人员会在工作时间内优先为您处理。如有紧急问题，请留下详细描述。",
        "weekend": "您好！现在是周末时间，您的问题已记录，客服人员会在下个工作日优先为您处理。如有紧急问题，请留下详细描述。",
        "holiday": "您好！现在是节假日期间，您的问题已记录，客服人员会在工作日恢复后优先为您处理。感谢您的理解！"
    }
    
    print(f"\n对应的响应消息:")
    print(f"{default_responses[message_type]}")

def show_sql_examples():
    """显示SQL操作示例"""
    print("📝 数据库操作示例")
    print("=" * 50)
    
    print("🔍 查看所有配置:")
    print("SELECT * FROM work_schedule_config ORDER BY config_key;")
    print()
    
    print("🔍 查看特定配置:")
    print("SELECT config_value FROM work_schedule_config WHERE config_key='work_start';")
    print()
    
    print("⚙️ 常用配置修改:")
    print()
    
    print("-- 修改工作时间")
    print("UPDATE work_schedule_config SET config_value='08:00' WHERE config_key='work_start';")
    print("UPDATE work_schedule_config SET config_value='17:00' WHERE config_key='work_end';")
    print()
    
    print("-- 修改工作日（周一到周六）")
    print("UPDATE work_schedule_config SET config_value='[1,2,3,4,5,6]' WHERE config_key='work_days';")
    print()
    
    print("-- 启用/禁用节假日模式")
    print("UPDATE work_schedule_config SET config_value='true' WHERE config_key='holiday_mode';")
    print("UPDATE work_schedule_config SET config_value='false' WHERE config_key='holiday_mode';")
    print()
    
    print("-- 启用/禁用工作时间功能")
    print("UPDATE work_schedule_config SET config_value='true' WHERE config_key='enabled';")
    print("UPDATE work_schedule_config SET config_value='false' WHERE config_key='enabled';")
    print()
    
    print("-- 修改响应消息")
    print("UPDATE work_schedule_config SET config_value='客服在线，立即为您服务！' WHERE config_key='work_hours_response';")
    print("UPDATE work_schedule_config SET config_value='现在是下班时间，明天上班后会优先处理您的问题。' WHERE config_key='non_work_hours_response';")
    print()
    
    print("🗑️ 重置为默认配置:")
    print("DELETE FROM work_schedule_config;")
    print("-- 然后重启机器人程序，会自动插入默认配置")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        show_usage()
        return
    
    command = sys.argv[1].lower()
    
    if command in ['help', '--help', '-h']:
        show_usage()
    elif command in ['status', 'time']:
        show_current_time_status()
    elif command in ['sql', 'examples']:
        show_sql_examples()
    else:
        print(f"❌ 未知命令: {command}")
        print("使用 'help' 查看帮助信息")

if __name__ == "__main__":
    main()
