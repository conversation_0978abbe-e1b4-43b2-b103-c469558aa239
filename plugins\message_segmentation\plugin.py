"""
消息分段回复插件

功能：
1. 根据配置文件控制回复消息的分段方式
2. 支持多种分段模式：
   - none: 不分段，全部输出
   - force: 强制分段回复
   - auto: 根据字数自动分段
   - probability: 概率分段回复
3. 可配置字数阈值和分段间隔时间

优先级：-100 (最低优先级，作为全局处理器)
"""

import time
import random
import re
from typing import List, Optional
from core.plugin_base import Plugin
from utils.config_utils import load_config


class MessageSegmentationPlugin(Plugin):
    """消息分段回复插件"""

    priority = -100  # 最低优先级，作为全局处理器

    def __init__(self, handler):
        super().__init__(handler)
        self.config = load_config().get('message_segmentation', {})
        self.enabled = self.config.get('enabled', True)
        self.mode = self.config.get('mode', 'auto')
        self.max_length = self.config.get('max_length', 500)
        self.probability = self.config.get('probability', 0.3)
        self.segment_delay = self.config.get('segment_delay', 1.5)

        self.handler._log(f"[分段回复] 插件已加载 - 模式: {self.mode}, 阈值: {self.max_length}字")

    def on_messages(self, chat: str, messages: List[str]) -> Optional[str]:
        """
        此插件不直接处理消息，而是提供分段发送的工具方法
        返回 None 让其他插件继续处理
        """
        return None

    def should_segment(self, message: str) -> bool:
        """
        判断消息是否需要分段

        Args:
            message: 要发送的消息内容

        Returns:
            bool: 是否需要分段
        """
        if not self.enabled:
            return False

        message_length = len(message)

        if self.mode == 'none':
            return False
        elif self.mode == 'force':
            return message_length > 50  # 强制模式下，超过50字就分段
        elif self.mode == 'auto':
            return message_length > self.max_length
        elif self.mode == 'probability':
            if message_length > self.max_length:
                return random.random() < self.probability
            return False
        else:
            return False

    def segment_message(self, message: str) -> List[str]:
        """
        将长消息分段

        Args:
            message: 原始消息

        Returns:
            List[str]: 分段后的消息列表
        """
        if not self.should_segment(message):
            return [message]

        segments = []
        current_segment = ""

        # 按句子分割（以句号、问号、感叹号为分界）
        sentences = re.split(r'([。！？\n])', message)

        for i in range(0, len(sentences), 2):
            if i + 1 < len(sentences):
                sentence = sentences[i] + sentences[i + 1]
            else:
                sentence = sentences[i]

            # 如果当前段落加上新句子超过阈值，则开始新段落
            if len(current_segment + sentence) > self.max_length and current_segment:
                segments.append(current_segment.strip())
                current_segment = sentence
            else:
                current_segment += sentence

        # 添加最后一段
        if current_segment.strip():
            segments.append(current_segment.strip())

        # 如果分段后只有一段且长度仍然很长，则强制按字数分割
        if len(segments) == 1 and len(segments[0]) > self.max_length * 1.5:
            return self._force_split_by_length(segments[0])

        return segments if len(segments) > 1 else [message]

    def _force_split_by_length(self, text: str) -> List[str]:
        """
        按固定长度强制分割文本

        Args:
            text: 要分割的文本

        Returns:
            List[str]: 分割后的文本列表
        """
        segments = []
        for i in range(0, len(text), self.max_length):
            segment = text[i:i + self.max_length]
            if i + self.max_length < len(text):
                segment += "..."
            segments.append(segment)
        return segments

    def send_segmented_message(self, wx, chat: str, message: str, at_users: List[str] = None):
        """
        发送分段消息 - 每个分段作为独立消息发送

        Args:
            wx: 微信实例
            chat: 聊天窗口名称
            message: 要发送的消息
            at_users: 需要@的用户列表（仅群聊）
        """
        segments = self.segment_message(message)

        if len(segments) == 1:
            # 不需要分段，直接发送
            self._send_single_message(wx, chat, segments[0], at_users)
            self.handler._log(f"[分段回复] 发送单条消息: {len(segments[0])}字")
        else:
            # 多条消息分段发送
            self.handler._log(f"[分段回复] 消息将分为 {len(segments)} 条独立消息发送")

            for i, segment in enumerate(segments):
                # 第一条消息包含@信息，后续消息不包含
                current_at_users = at_users if i == 0 else None

                # 为非最后一条消息添加"(续)"标识
                if i < len(segments) - 1:
                    display_segment = segment + " (续)"
                else:
                    display_segment = segment

                # 发送当前分段作为独立消息
                self._send_single_message(wx, chat, display_segment, current_at_users)
                self.handler._log(f"[分段回复] 已发送第 {i+1}/{len(segments)} 条消息: {len(display_segment)}字")

                # 分段间延迟（除了最后一条）
                if i < len(segments) - 1:
                    self.handler._log(f"[分段回复] 等待 {self.segment_delay} 秒后发送下一条...")
                    time.sleep(self.segment_delay)

    def _send_single_message(self, wx, chat: str, message: str, at_users: List[str] = None):
        """
        发送单条消息

        Args:
            wx: 微信实例
            chat: 聊天窗口名称
            message: 消息内容
            at_users: 需要@的用户列表
        """
        try:
            # 构建最终消息文本
            if at_users:
                at_part = ''.join([f"{{@{user}}}" for user in at_users])
                final_message = f"{at_part}\n{message}"
            else:
                final_message = message

            # 发送消息 - 优先使用SendTypingText（群聊），回退到SendMsg
            try:
                if at_users:  # 群聊
                    try:
                        wx.SendTypingText(final_message, who=chat)
                    except TypeError:
                        wx.SendTypingText(chat, final_message)
                else:  # 私聊
                    wx.SendMsg(final_message, who=chat)
            except Exception as send_error:
                # 回退到SendMsg
                self.handler._log(f"[分段回复] SendTypingText失败，使用SendMsg: {send_error}", 'DEBUG')
                wx.SendMsg(final_message, who=chat)

            self.handler._log(f"[分段回复] 已发送: {final_message[:50]}...")

        except Exception as e:
            self.handler._log(f"[分段回复] 发送失败: {e}", 'ERROR')

    def get_config_info(self) -> str:
        """
        获取当前配置信息

        Returns:
            str: 配置信息字符串
        """
        return (
            f"分段回复配置:\n"
            f"- 启用状态: {'是' if self.enabled else '否'}\n"
            f"- 分段模式: {self.mode}\n"
            f"- 字数阈值: {self.max_length}\n"
            f"- 分段概率: {self.probability}\n"
            f"- 间隔时间: {self.segment_delay}秒"
        )
