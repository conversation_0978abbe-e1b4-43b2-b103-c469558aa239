import json
import requests

# ====== 配置区域（请根据实际情况修改）=======
API_KEY = "app-Ij7mlSjS7WvnKWoX8V7clz1R"  # 替换为你自己的 API Key
URL = "http://111.230.24.48:81/v1/workflows/run"
CONTEXT = "你好，请帮我写一首诗。"  # 测试用的上下文内容
# ============================================

headers = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json"
}

payload = {
    "inputs": {"astrbot_text_query": CONTEXT},
    "response_mode": "streaming",
    "user": "terminal_user"
}

print("🚀 正在发送请求...")
print("请求体：", payload)

final_answer = ""
try:
    with requests.post(URL, headers=headers, json=payload, stream=True, timeout=60) as response:
        print(f"\n🌐 状态码: {response.status_code}")
        if response.status_code != 200:
            print("❌ 请求失败，响应内容：")
            print(response.text)
        else:
            for line in response.iter_lines():
                if line:
                    decoded_line = line.decode('utf-8')
                    if decoded_line.startswith("data:"):
                        data_part = decoded_line[5:].strip()
                        try:
                            json_data = json.loads(data_part)
                            event = json_data.get("event")

                            if event == "tts_message":
                                chunk = json_data.get("audio", "")
                                final_answer += chunk
                                print("🎙️ 收到音频片段：", chunk)

                            elif event == "tts_message_end":
                                print("\n✅ 完整回复已接收完毕。")
                                break

                        except json.JSONDecodeError as e:
                            print("⚠️ JSON 解码失败：", str(e))
                            continue

except Exception as e:
    print(f"\n🚨 发生异常：{e}")

if final_answer.strip():
    print("\n🟢 最终回复内容：")
    print(final_answer)
else:
    print("\n🟡 未收到有效回复内容。可能是工作流中未配置输出节点？")