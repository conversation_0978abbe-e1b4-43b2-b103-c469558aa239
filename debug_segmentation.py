#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试分段逻辑
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

def debug_segmentation():
    """调试分段逻辑"""
    print("🔍 调试分段逻辑")
    print("=" * 50)
    
    # 创建模拟handler
    class MockHandler:
        def _log(self, message, level='INFO'):
            print(f"[{level}] {message}")
    
    # 导入插件
    from plugins.message_segmentation.plugin import MessageSegmentationPlugin
    
    # 创建插件实例
    handler = MockHandler()
    plugin = MessageSegmentationPlugin(handler)
    
    print(f"📋 当前配置:")
    print(f"   - 启用状态: {plugin.enabled}")
    print(f"   - 分段模式: {plugin.mode}")
    print(f"   - 字数阈值: {plugin.max_length}")
    
    # 测试消息（179字，应该超过100字阈值）
    test_message = '''戏精附体版：  
"辣的！宝子这要求我太懂了（搓手手）海底捞新出的麻辣锅底配酥肉，辣到头皮发麻那种～咱直接安排上啊！🔥"

网络热梗版：  
"姐妹尊嘟要吃辣？建议狠狠点外卖来份变态辣炸鸡🍗绝绝子级别的辣味，CPU我都要燃烧起来了哈哈哈～"

温和婉转版：  
"想吃辣的啦？那咱们煮个简单的辣酱拌面吧～加点葱花和辣椒油，暖胃又过瘾（轻声）要不要试试看呢？"'''
    
    print(f"\n📝 测试消息:")
    print(f"   长度: {len(test_message)}字")
    print(f"   内容: {test_message}")
    
    # 测试是否需要分段
    should_segment = plugin.should_segment(test_message)
    print(f"\n🔍 分段判断:")
    print(f"   需要分段: {should_segment}")
    print(f"   判断依据: 长度{len(test_message)} > 阈值{plugin.max_length} = {len(test_message) > plugin.max_length}")
    
    # 测试分段结果
    segments = plugin.segment_message(test_message)
    print(f"\n📄 分段结果:")
    print(f"   分段数量: {len(segments)}")
    
    for i, segment in enumerate(segments, 1):
        print(f"\n   段落{i} ({len(segment)}字):")
        print(f"   {segment}")
    
    # 测试分段算法的详细过程
    print(f"\n🔧 分段算法调试:")
    import re
    sentences = re.split(r'([。！？\n])', test_message)
    print(f"   句子分割结果: {len(sentences)}个部分")
    for i, sentence in enumerate(sentences):
        if sentence.strip():
            print(f"     {i}: '{sentence}'")

if __name__ == "__main__":
    debug_segmentation()
