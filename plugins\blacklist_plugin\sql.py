"""
Blacklist Plugin SQL Functions
包含黑名单插件所需的所有SQL查询函数
"""
from typing import Optional, List, Dict


def get_all_blacklist_entries(self) -> List[Dict]:
    """获取所有黑名单条目"""
    cursor = self.connection.cursor(dictionary=True)
    cursor.execute("""
        SELECT chat_name, chat_type, is_global, created_at, updated_at
        FROM blacklist_entries
        WHERE enabled = TRUE
        ORDER BY created_at DESC
    """)
    return cursor.fetchall()


def get_blacklist_entry_by_chat(self, chat_name: str) -> Optional[Dict]:
    """根据聊天名称获取黑名单条目"""
    cursor = self.connection.cursor(dictionary=True)
    cursor.execute("""
        SELECT * FROM blacklist_entries
        WHERE chat_name = %s AND enabled = TRUE
    """, (chat_name,))
    return cursor.fetchone()


def add_blacklist_entry(self, chat_name: str, chat_type: str = "group", is_global: bool = False) -> bool:
    """添加黑名单条目"""
    cursor = self.connection.cursor()
    cursor.execute("""
        INSERT INTO blacklist_entries (chat_name, chat_type, is_global, enabled, created_at, updated_at)
        VALUES (%s, %s, %s, TRUE, NOW(), NOW())
        ON DUPLICATE KEY UPDATE
        enabled = TRUE, is_global = VALUES(is_global), updated_at = NOW()
    """, (chat_name, chat_type, is_global))
    self.connection.commit()
    return cursor.rowcount > 0


def remove_blacklist_entry(self, chat_name: str) -> bool:
    """从黑名单移除条目（软删除）"""
    cursor = self.connection.cursor()
    cursor.execute("""
        UPDATE blacklist_entries 
        SET enabled = FALSE, updated_at = NOW()
        WHERE chat_name = %s
    """, (chat_name,))
    self.connection.commit()
    return cursor.rowcount > 0


def delete_blacklist_entry(self, chat_name: str) -> bool:
    """永久删除黑名单条目"""
    cursor = self.connection.cursor()
    cursor.execute("""
        DELETE FROM blacklist_entries WHERE chat_name = %s
    """, (chat_name,))
    self.connection.commit()
    return cursor.rowcount > 0


def get_blacklist_stats(self) -> Dict:
    """获取黑名单统计信息"""
    cursor = self.connection.cursor(dictionary=True)
    cursor.execute("""
        SELECT 
            COUNT(*) as total_entries,
            SUM(CASE WHEN chat_type = 'group' THEN 1 ELSE 0 END) as group_entries,
            SUM(CASE WHEN chat_type = 'private' THEN 1 ELSE 0 END) as private_entries,
            SUM(CASE WHEN is_global = TRUE THEN 1 ELSE 0 END) as global_entries
        FROM blacklist_entries
        WHERE enabled = TRUE
    """)
    return cursor.fetchone() or {}


def update_blacklist_entry(self, chat_name: str, chat_type: str = None, is_global: bool = None) -> bool:
    """更新黑名单条目"""
    updates = []
    params = []

    if chat_type is not None:
        updates.append("chat_type = %s")
        params.append(chat_type)
    if is_global is not None:
        updates.append("is_global = %s")
        params.append(is_global)

    if not updates:
        return False

    updates.append("updated_at = NOW()")
    params.append(chat_name)

    cursor = self.connection.cursor()
    cursor.execute(f"""
        UPDATE blacklist_entries 
        SET {', '.join(updates)}
        WHERE chat_name = %s AND enabled = TRUE
    """, params)
    self.connection.commit()
    return cursor.rowcount > 0


# 导出所有SQL函数
BLACKLIST_SQL_FUNCTIONS = {
    'get_all_blacklist_entries': get_all_blacklist_entries,
    'get_blacklist_entry_by_chat': get_blacklist_entry_by_chat,
    'add_blacklist_entry': add_blacklist_entry,
    'remove_blacklist_entry': remove_blacklist_entry,
    'delete_blacklist_entry': delete_blacklist_entry,
    'get_blacklist_stats': get_blacklist_stats,
    'update_blacklist_entry': update_blacklist_entry,
}