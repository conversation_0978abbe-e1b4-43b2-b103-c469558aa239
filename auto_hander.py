import re
import time
import traceback
from datetime import datetime, timedelta
from typing import Dict, <PERSON>, Union, DefaultDict
from collections import defaultdict

from wxautox import WeChat
from plugins import load_plugins
from utils.config_utils import load_config
from group_reply import handle_group_message
from private_reply import handle_private_message


class WeChatMessageHandler:
    QUIET_SEC = 6          # 静默阈值（秒）
    INACTIVE_MIN = 1       # 监听超时（分钟）
    DEBOUNCE_WINDOW = 3.0  # 防抖窗口时间（秒）

    def __init__(self):
        self.wx = WeChat('Elik')
        self.buffers: Dict[str, List[object]] = {}  # 缓存消息
        self.last_msg_time: Dict[str, float] = {}  # 最后一条消息时间
        self.listened: Dict[str, datetime] = {}  # 当前监听的聊天窗口
        self.last_sender_time: Dict[str, float] = {}  # 防抖用：记录每个发送者最后发消息的时间
        self.chat_type_cache: Dict[str, str] = {}  # 新增：缓存聊天类型 {'chat_name': 'group'/'friend'}

        # 【新增】加载配置并初始化数据库连接
        cfg = load_config()
        db_config = cfg.get("mysql", {})
        if not db_config:
            raise ValueError("❌ config.json 中未配置 mysql 数据库信息")

        from db_plugins.mysql import MySQLDB
        self.db = MySQLDB(**db_config)
        self.db.connect()

        # 加载插件
        self.plugins = load_plugins(self)

        self._log('系统初始化成功，已连接到微信客户端')

    @staticmethod
    def _safe_name(x: Union[str, bytes, None]) -> str:
        if x is None:
            return ''
        if isinstance(x, bytes):
            x = x.decode('utf-8', errors='ignore')
        return str(x).strip()

    @classmethod
    def _normalize_title(cls, title: str) -> str:
        title = cls._safe_name(title)
        return re.sub(r"\s*\(\d+\)\s*$", "", title)

    def _log(self, msg: str, level: str = 'INFO'):
        print(f"{datetime.now():%Y-%m-%d %H:%M:%S} [微信消息处理] [{level}] {msg}")

    def run(self):
        self._log('开始消息监听循环…')
        try:
            while True:
                now_ts = time.time()
                for chat_wnd, msgs in (self.wx.GetListenMessage() or {}).items():
                    chat = self._normalize_title(getattr(chat_wnd, 'name', getattr(chat_wnd, 'who', '')))
                    self._cache_msgs(chat, msgs)

                for raw_chat, msgs in (self.wx.GetNextNewMessage() or {}).items():
                    chat = self._normalize_title(raw_chat)
                    self._cache_msgs(chat, msgs)

                for chat in list(self.buffers.keys()):
                    if now_ts - self.last_msg_time.get(chat, 0) >= self.QUIET_SEC:
                        self._handle_buffer(chat)

                timeout = timedelta(minutes=self.INACTIVE_MIN)
                for chat, ts in list(self.listened.items()):
                    if datetime.now() - ts > timeout:
                        try:
                            self.wx.RemoveListenChat(chat)
                        except Exception:
                            pass
                        self.listened.pop(chat, None)
                        self.chat_type_cache.pop(chat, None)  # 同步清理缓存
                        self._log(f"监听超时移除 {chat}")
                time.sleep(0.5)
        except KeyboardInterrupt:
            self._log('停止…')
        except Exception as e:
            traceback.print_exc()
            self._log(f'主循环异常: {e}', 'ERROR')
        finally:
            for chat in list(self.listened):
                try:
                    self.wx.RemoveListenChat(chat)
                except Exception:
                    pass
            self._log('监听清理完毕')

    def _try_add_listen(self, chat: str):
        chat = self._normalize_title(chat)

        # 🚫 检查黑名单 - 如果在黑名单中则不添加监听
        for plugin in self.plugins:
            if plugin.__class__.__name__ == 'BlacklistPlugin':
                try:
                    reply = plugin.on_messages(chat, [""])  # 传入空消息进行黑名单检查
                    if reply == "__BLACKLISTED__":
                        self._log(f"[系统] 聊天 '{chat}' 在黑名单中，不添加监听", 'DEBUG')
                        return
                except Exception as e:
                    self._log(f"[黑名单插件异常] {e}", 'ERROR')
                break

        if not chat or chat in self.listened:
            return

        for attempt in (1, 2):
            try:
                if attempt == 2:
                    self.wx.ChatWith(chat, exact=True)
                    time.sleep(0.8)
                ok = self.wx.AddListenChat(chat)
                if ok or chat in getattr(self.wx, 'listen', {}):
                    self.listened[chat] = datetime.now()

                    # 更新聊天类型缓存
                    chat_wnd = self.wx.listen.get(chat)
                    if chat_wnd:
                        chat_info = chat_wnd.ChatInfo()
                        chat_type = chat_info.get('chat_type', 'friend')
                        self.chat_type_cache[chat] = chat_type
                        self._log(f"[系统] '{chat}' 类型为：{'群聊' if chat_type == 'group' else '私聊'}")

                    self._log(f"已将 '{chat}' 添加到监听模式 (attempt {attempt})")
                    return
            except Exception as e:
                self._log(f"AddListenChat 失败 (attempt {attempt}): {e}", 'WARNING')
        self._log(f"监听 '{chat}' 最终失败", 'ERROR')

    def _mark_read(self, chat: str):
        chat = self._normalize_title(chat)
        try:
            self.wx.ChatWith(chat, exact=True)
            time.sleep(0.5)
            self._log(f"已将 '{chat}' 的消息标记为已读", 'DEBUG')
        except Exception as e:
            self._log(f'切换聊天窗口失败 (无法标记为已读): {e}', 'WARNING')

    def _cache_msgs(self, chat: str, msgs):
        chat = self._normalize_title(chat)

        # 提前获取并缓存聊天类型
        if chat not in self.chat_type_cache:
            try:
                self.wx.ChatWith(chat, exact=True)
                chat_info = self.wx.CurrentChat(details=True)
                chat_type = chat_info.get('chat_type', 'friend')
                self.chat_type_cache[chat] = chat_type
                self._log(f"[系统] '{chat}' 类型为：{'群聊' if chat_type == 'group' else '私聊'}")
            except Exception as e:
                self._log(f"无法获取聊天类型 '{chat}': {e}", 'WARNING')
                self.chat_type_cache[chat] = 'friend'

        # 🚫 首先进行黑名单检查 - 最高优先级
        for plugin in self.plugins:
            if plugin.__class__.__name__ == 'BlacklistPlugin':
                try:
                    contents_for_check = [getattr(m, 'content', '') for m in msgs if getattr(m, 'type', '') == 'friend']
                    if contents_for_check:
                        reply = plugin.on_messages(chat, contents_for_check)
                        if reply == "__BLACKLISTED__":
                            self._log(f"[系统] 消息来自黑名单聊天 '{chat}'，跳过缓存", 'DEBUG')
                            return
                except Exception as e:
                    self._log(f"[黑名单插件异常] {e}", 'ERROR')
                break

        contents = [m for m in msgs if getattr(m, 'type', '') == 'friend']
        if not contents:
            return

        now = time.time()
        for msg in contents:
            sender = getattr(msg, 'sender_remark', getattr(msg, 'sender', '未知用户'))
            content = getattr(msg, 'content', '').strip()
            key = f"{chat}_{sender}"

            if key in self.last_sender_time:
                if now - self.last_sender_time[key] < self.DEBOUNCE_WINDOW:
                    self._log(f"防抖机制生效，延迟处理 '{chat}' 中 '{sender}' 的新消息", 'DEBUG')
                    self.buffers.setdefault(chat, []).append(msg)
                    self.last_msg_time[chat] = now
                    continue

            self.last_sender_time[key] = now
            self.buffers.setdefault(chat, []).append(msg)
            self.last_msg_time[chat] = now

        if chat in self.listened:
            self.listened[chat] = datetime.now()

        self._log(f"DEBUG: 缓存 {len(contents)} 条消息到 '{chat}' (累计 {len(self.buffers[chat])})", 'DEBUG')

    def _handle_buffer(self, chat: str):
        msgs = self.buffers.pop(chat, [])
        if not msgs:
            return

        cfg = load_config()
        bot_name = cfg.get("bot_name", getattr(self.wx, 'nickname', '默认昵称'))

        user_messages = []
        for msg in msgs:
            sender = getattr(msg, 'sender_remark', getattr(msg, 'sender', '未知用户'))
            content = getattr(msg, 'content', '').strip()

            at_me = False
            m = re.search(r"@([^@]+)", content)
            if m:
                # at_name = m.group(1).strip()
                at_name = re.sub(r"[\s\u200B-\u200D\u202F\u2060\uFEFF]", "", m.group(1))
                self._log(f"[关键词回复] 【{chat}】解析到 @ 名称为: '{at_name}' (原始内容: '{content}')",
                                  level='DEBUG')
                self._log(f"[关键词回复] 【{chat}】机器人名称为: '{bot_name}'", level='DEBUG')
                if at_name == bot_name:
                    at_me = True

            user_messages.append({
                "sender": sender,
                "content": content,
                "at_me": at_me
            })

        senders = set(um["sender"] for um in user_messages)
        self._log(f"INFO: '{chat}' 接收到 {len(user_messages)} 条消息，来自用户：{', '.join(senders)}")

        grouped = defaultdict(list)
        for um in user_messages:
            grouped[(chat, um['sender'])].append(um)

        for key, messages in grouped.items():
            chat_name, sender = key
            contents = [m['content'] for m in messages]

            # 获取聊天类型
            chat_type = self.chat_type_cache.get(chat_name, 'friend')
            is_group = chat_type == 'group'

            if is_group:
                handle_group_message(self.wx, chat_name, contents, messages, self.plugins, self._log)
            else:
                handle_private_message(self.wx, chat_name, contents, messages, self.plugins, self._log)

        self._mark_read(chat)
        self._try_add_listen(chat)