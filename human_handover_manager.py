#!/usr/bin/env python3
"""
私聊转人工管理工具
用于管理转人工关键词、查看转人工记录、用户标签等
"""

import os
import sys
import json
from datetime import datetime
from utils.config_utils import load_config
from db_plugins.mysql import MySQLDB


class HumanHandoverManager:
    """私聊转人工管理器"""
    
    def __init__(self):
        # 加载配置
        self.config = load_config()
        db_config = self.config.get("mysql", {})
        if not db_config:
            raise ValueError("❌ config.json 中未配置 mysql 数据库信息")
        
        # 连接数据库
        self.db = MySQLDB(**db_config)
        self.db.connect()
        
        # 注册SQL函数
        from plugins.human_handover.sql import HUMAN_HANDOVER_SQL_FUNCTIONS
        self.db.register_plugin_functions("HumanHandoverManager", HUMAN_HANDOVER_SQL_FUNCTIONS)
        
        # 初始化表
        try:
            self.db.init_human_handover_tables()
            print("✅ 数据库表初始化完成")
        except Exception as e:
            print(f"❌ 初始化数据库表失败: {e}")

    def list_keywords(self):
        """列出所有触发关键词"""
        try:
            keywords = self.db.get_handover_keywords()
            
            if not keywords:
                print("📋 暂无转人工触发关键词")
                return
            
            print(f"📋 共有 {len(keywords)} 个转人工触发关键词:")
            print("-" * 40)
            
            for keyword in keywords:
                print(f"关键词: {keyword['keyword']} (优先级: {keyword['priority']})")
            
        except Exception as e:
            print(f"❌ 获取关键词列表失败: {e}")

    def add_keyword(self, keyword: str, priority: int = 0):
        """添加触发关键词"""
        try:
            success = self.db.add_handover_keyword(keyword, priority)
            if success:
                print(f"✅ 关键词添加成功: {keyword}")
            else:
                print(f"❌ 关键词添加失败: {keyword}")
        except Exception as e:
            print(f"❌ 添加关键词失败: {e}")

    def remove_keyword(self, keyword: str):
        """删除触发关键词"""
        try:
            success = self.db.remove_handover_keyword(keyword)
            if success:
                print(f"✅ 关键词删除成功: {keyword}")
            else:
                print(f"❌ 关键词删除失败或不存在: {keyword}")
        except Exception as e:
            print(f"❌ 删除关键词失败: {e}")

    def list_handover_records(self, status: str = None, limit: int = 20):
        """列出转人工记录"""
        try:
            records = self.db.get_handover_records(status=status, limit=limit)
            
            if not records:
                status_text = f"状态为 '{status}' 的" if status else ""
                print(f"📋 暂无{status_text}转人工记录")
                return
            
            status_text = f" (状态: {status})" if status else ""
            print(f"📋 转人工记录{status_text} (最近 {len(records)} 条):")
            print("-" * 80)
            
            for record in records:
                status_icon = {"pending": "⏳", "handled": "✅", "closed": "🔒"}.get(record['status'], "❓")
                blacklist_icon = "🚫" if record['auto_blacklisted'] else ""
                
                print(f"{status_icon} ID: {record['id']} - {record['user_name']} {blacklist_icon}")
                print(f"   触发关键词: {record['trigger_keyword']}")
                print(f"   触发时间: {record['handover_time']}")
                print(f"   触发消息: {record['trigger_message']}")
                if record['context_summary']:
                    print(f"   AI总结: {record['context_summary']}")
                if record['handler_notes']:
                    print(f"   处理备注: {record['handler_notes']}")
                print("-" * 40)
                
        except Exception as e:
            print(f"❌ 获取转人工记录失败: {e}")

    def show_user_profile(self, user_name: str):
        """显示用户档案"""
        try:
            # 获取用户标签
            tags = self.db.get_user_tags(user_name)
            
            # 获取用户统计
            stats = self.db.get_user_statistics(user_name)
            
            # 获取转人工记录
            records = self.db.get_handover_records(user_name=user_name, limit=10)
            
            print(f"👤 用户档案: {user_name}")
            print("=" * 50)
            
            # 统计信息
            print("【统计信息】")
            print(f"转人工次数: {stats.get('handover_count', 0)}")
            print(f"最后转人工: {stats.get('last_handover_time', '无')}")
            print(f"标签数量: {stats.get('tag_count', 0)}")
            print()
            
            # 用户标签
            print("【用户标签】")
            if tags:
                for tag in tags:
                    confidence_str = f"({tag['confidence']:.1%})" if tag.get('confidence') else ""
                    tag_type_str = f"[{tag['tag_type']}]" if tag.get('tag_type') else ""
                    print(f"- {tag['tag_name']}: {tag['tag_value']} {confidence_str} {tag_type_str}")
            else:
                print("暂无标签")
            print()
            
            # 转人工记录
            print("【转人工记录】")
            if records:
                for record in records:
                    status_icon = {"pending": "⏳", "handled": "✅", "closed": "🔒"}.get(record['status'], "❓")
                    print(f"{status_icon} {record['handover_time']} - {record['trigger_keyword']}")
                    print(f"   消息: {record['trigger_message']}")
                    if record['context_summary']:
                        print(f"   总结: {record['context_summary']}")
                    print()
            else:
                print("暂无转人工记录")
                
        except Exception as e:
            print(f"❌ 获取用户档案失败: {e}")

    def search_users_by_tag(self, tag_name: str, tag_value: str = None):
        """根据标签搜索用户"""
        try:
            users = self.db.search_users_by_tag(tag_name, tag_value)
            
            if not users:
                search_text = f"标签 '{tag_name}'" + (f" 值包含 '{tag_value}'" if tag_value else "")
                print(f"📋 未找到具有{search_text}的用户")
                return
            
            search_text = f"标签 '{tag_name}'" + (f" 值包含 '{tag_value}'" if tag_value else "")
            print(f"📋 具有{search_text}的用户 ({len(users)} 个):")
            print("-" * 50)
            
            for user in users:
                confidence_str = f"({user['confidence']:.1%})" if user.get('confidence') else ""
                print(f"用户: {user['user_name']}")
                print(f"标签值: {user['tag_value']} {confidence_str}")
                print(f"创建时间: {user['created_at']}")
                print("-" * 30)
                
        except Exception as e:
            print(f"❌ 搜索用户失败: {e}")

    def show_statistics(self):
        """显示统计信息"""
        try:
            # 获取转人工记录
            all_records = self.db.get_handover_records(limit=1000)
            pending_records = self.db.get_handover_records(status='pending', limit=1000)
            
            # 获取标签统计
            tag_summary = self.db.get_all_tags_summary()
            
            # 计算今日转人工数量
            today = datetime.now().date()
            today_count = sum(1 for record in all_records 
                            if record['handover_time'].date() == today)
            
            print("📊 转人工统计信息")
            print("=" * 50)
            
            print("【转人工统计】")
            print(f"今日转人工: {today_count} 次")
            print(f"待处理数量: {len(pending_records)} 个")
            print(f"总记录数: {len(all_records)} 个")
            print()
            
            print("【标签统计】")
            if tag_summary:
                print(f"标签种类: {len(tag_summary)} 种")
                print("热门标签:")
                for tag in tag_summary[:10]:  # 显示前10个
                    avg_conf = f"{tag['avg_confidence']:.1%}" if tag['avg_confidence'] else "N/A"
                    print(f"- {tag['tag_name']}: {tag['user_count']} 用户 (平均置信度: {avg_conf})")
            else:
                print("暂无标签数据")
            print()
            
            # 关键词统计
            keywords = self.db.get_handover_keywords()
            print("【关键词统计】")
            print(f"触发关键词: {len(keywords)} 个")
            
        except Exception as e:
            print(f"❌ 获取统计信息失败: {e}")

    def update_record_status(self, record_id: int, status: str, notes: str = None):
        """更新转人工记录状态"""
        try:
            valid_statuses = ['pending', 'handled', 'closed']
            if status not in valid_statuses:
                print(f"❌ 无效状态，请使用: {', '.join(valid_statuses)}")
                return
            
            success = self.db.update_handover_record(record_id, status, notes)
            if success:
                print(f"✅ 记录状态更新成功: ID {record_id} -> {status}")
                if notes:
                    print(f"   备注: {notes}")
            else:
                print(f"❌ 记录状态更新失败或记录不存在: ID {record_id}")
        except Exception as e:
            print(f"❌ 更新记录状态失败: {e}")

    def export_records_to_file(self, filename: str = None, status: str = None):
        """导出转人工记录到文件"""
        try:
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                status_suffix = f"_{status}" if status else ""
                filename = f"handover_records{status_suffix}_{timestamp}.txt"
            
            records = self.db.get_handover_records(status=status, limit=1000)
            
            if not records:
                print("❌ 没有记录可导出")
                return
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("转人工记录导出\n")
                f.write("=" * 60 + "\n")
                f.write(f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"记录数量: {len(records)}\n")
                if status:
                    f.write(f"状态筛选: {status}\n")
                f.write("=" * 60 + "\n\n")
                
                for record in records:
                    f.write(f"记录ID: {record['id']}\n")
                    f.write(f"用户: {record['user_name']}\n")
                    f.write(f"触发关键词: {record['trigger_keyword']}\n")
                    f.write(f"触发时间: {record['handover_time']}\n")
                    f.write(f"状态: {record['status']}\n")
                    f.write(f"触发消息: {record['trigger_message']}\n")
                    
                    if record['context_summary']:
                        f.write(f"AI总结: {record['context_summary']}\n")
                    
                    if record['user_tags']:
                        try:
                            tags = json.loads(record['user_tags'])
                            f.write("用户标签:\n")
                            for tag in tags:
                                f.write(f"  - {tag['tag_name']}: {tag['tag_value']}\n")
                        except:
                            pass
                    
                    if record['handler_notes']:
                        f.write(f"处理备注: {record['handler_notes']}\n")
                    
                    f.write("-" * 40 + "\n\n")
            
            print(f"✅ 记录导出成功: {filename}")
            
        except Exception as e:
            print(f"❌ 导出记录失败: {e}")

    def close(self):
        """关闭数据库连接"""
        if self.db:
            self.db.close()


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("私聊转人工管理工具")
        print("=" * 50)
        print("用法:")
        print("  python human_handover_manager.py list_keywords              # 列出触发关键词")
        print("  python human_handover_manager.py add_keyword <关键词>       # 添加关键词")
        print("  python human_handover_manager.py remove_keyword <关键词>    # 删除关键词")
        print("  python human_handover_manager.py list_records [状态]        # 列出转人工记录")
        print("  python human_handover_manager.py user_profile <用户名>      # 查看用户档案")
        print("  python human_handover_manager.py search_tag <标签名> [值]   # 根据标签搜索用户")
        print("  python human_handover_manager.py statistics                 # 显示统计信息")
        print("  python human_handover_manager.py update_status <ID> <状态> [备注] # 更新记录状态")
        print("  python human_handover_manager.py export [状态]              # 导出记录")
        print()
        print("状态: pending(待处理), handled(已处理), closed(已关闭)")
        print()
        print("示例:")
        print('  python human_handover_manager.py add_keyword "技术支持"')
        print('  python human_handover_manager.py list_records pending')
        print('  python human_handover_manager.py user_profile "张三"')
        print('  python human_handover_manager.py search_tag "问题类型" "投诉"')
        print('  python human_handover_manager.py update_status 1 handled "已联系用户"')
        return
    
    try:
        manager = HumanHandoverManager()
        command = sys.argv[1].lower()
        
        if command == 'list_keywords':
            manager.list_keywords()
            
        elif command == 'add_keyword':
            if len(sys.argv) < 3:
                print("❌ 请提供关键词")
                return
            keyword = sys.argv[2]
            priority = int(sys.argv[3]) if len(sys.argv) > 3 else 0
            manager.add_keyword(keyword, priority)
            
        elif command == 'remove_keyword':
            if len(sys.argv) < 3:
                print("❌ 请提供关键词")
                return
            keyword = sys.argv[2]
            manager.remove_keyword(keyword)
            
        elif command == 'list_records':
            status = sys.argv[2] if len(sys.argv) > 2 else None
            manager.list_handover_records(status)
            
        elif command == 'user_profile':
            if len(sys.argv) < 3:
                print("❌ 请提供用户名")
                return
            user_name = sys.argv[2]
            manager.show_user_profile(user_name)
            
        elif command == 'search_tag':
            if len(sys.argv) < 3:
                print("❌ 请提供标签名")
                return
            tag_name = sys.argv[2]
            tag_value = sys.argv[3] if len(sys.argv) > 3 else None
            manager.search_users_by_tag(tag_name, tag_value)
            
        elif command == 'statistics':
            manager.show_statistics()
            
        elif command == 'update_status':
            if len(sys.argv) < 4:
                print("❌ 请提供记录ID和状态")
                return
            record_id = int(sys.argv[2])
            status = sys.argv[3]
            notes = sys.argv[4] if len(sys.argv) > 4 else None
            manager.update_record_status(record_id, status, notes)
            
        elif command == 'export':
            status = sys.argv[2] if len(sys.argv) > 2 else None
            manager.export_records_to_file(status=status)
            
        else:
            print(f"❌ 未知命令: {command}")
            
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if 'manager' in locals():
            manager.close()


if __name__ == "__main__":
    main()
