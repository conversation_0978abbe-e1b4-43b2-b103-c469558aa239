#!/usr/bin/env python3
"""
定时消息管理工具
用于管理定时发送消息的任务
"""

import os
import sys
from datetime import datetime, time, date
from utils.config_utils import load_config
from db_plugins.mysql import MySQLDB


class ScheduledMessageManager:
    """定时消息管理器"""
    
    def __init__(self):
        # 加载配置
        self.config = load_config()
        db_config = self.config.get("mysql", {})
        if not db_config:
            raise ValueError("❌ config.json 中未配置 mysql 数据库信息")
        
        # 连接数据库
        self.db = MySQLDB(**db_config)
        self.db.connect()
        
        # 注册SQL函数
        from plugins.scheduled_message.sql import SCHEDULED_MESSAGE_SQL_FUNCTIONS
        self.db.register_plugin_functions("ScheduledMessageManager", SCHEDULED_MESSAGE_SQL_FUNCTIONS)
        
        # 初始化表
        try:
            self.db.init_scheduled_message_tables()
            print("✅ 数据库表初始化完成")
        except Exception as e:
            print(f"❌ 初始化数据库表失败: {e}")

    def add_daily_task(self, task_name: str, chat_name: str, chat_type: str, 
                      message: str, send_time: str, max_count: int = None):
        """添加每日定时任务"""
        try:
            hour, minute = map(int, send_time.split(':'))
            schedule_time = time(hour, minute)
            
            success = self.db.add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_content=message,
                schedule_type='daily',
                schedule_time=schedule_time,
                max_send_count=max_count
            )
            
            if success:
                print(f"✅ 每日定时任务添加成功: {task_name}")
                print(f"   聊天: {chat_name} ({chat_type})")
                print(f"   时间: 每天 {send_time}")
                print(f"   消息: {message}")
                if max_count:
                    print(f"   最大发送次数: {max_count}")
            else:
                print(f"❌ 添加任务失败: {task_name}")
                
        except Exception as e:
            print(f"❌ 添加每日任务失败: {e}")

    def add_weekly_task(self, task_name: str, chat_name: str, chat_type: str,
                       message: str, weekday: int, send_time: str, max_count: int = None):
        """添加每周定时任务"""
        try:
            hour, minute = map(int, send_time.split(':'))
            schedule_time = time(hour, minute)
            
            weekday_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
            
            success = self.db.add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_content=message,
                schedule_type='weekly',
                schedule_time=schedule_time,
                schedule_weekday=weekday,
                max_send_count=max_count
            )
            
            if success:
                print(f"✅ 每周定时任务添加成功: {task_name}")
                print(f"   聊天: {chat_name} ({chat_type})")
                print(f"   时间: 每{weekday_names[weekday]} {send_time}")
                print(f"   消息: {message}")
                if max_count:
                    print(f"   最大发送次数: {max_count}")
            else:
                print(f"❌ 添加任务失败: {task_name}")
                
        except Exception as e:
            print(f"❌ 添加每周任务失败: {e}")

    def add_interval_task(self, task_name: str, chat_name: str, chat_type: str,
                         message: str, interval_minutes: int, max_count: int = None):
        """添加间隔定时任务"""
        try:
            success = self.db.add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_content=message,
                schedule_type='interval',
                interval_minutes=interval_minutes,
                max_send_count=max_count
            )
            
            if success:
                print(f"✅ 间隔定时任务添加成功: {task_name}")
                print(f"   聊天: {chat_name} ({chat_type})")
                print(f"   间隔: 每 {interval_minutes} 分钟")
                print(f"   消息: {message}")
                if max_count:
                    print(f"   最大发送次数: {max_count}")
            else:
                print(f"❌ 添加任务失败: {task_name}")
                
        except Exception as e:
            print(f"❌ 添加间隔任务失败: {e}")

    def add_once_task(self, task_name: str, chat_name: str, chat_type: str,
                     message: str, send_datetime: str):
        """添加一次性定时任务"""
        try:
            send_dt = datetime.strptime(send_datetime, "%Y-%m-%d %H:%M")
            
            success = self.db.add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_content=message,
                schedule_type='once',
                schedule_date=send_dt.date(),
                schedule_time=send_dt.time(),
                max_send_count=1
            )
            
            if success:
                print(f"✅ 一次性定时任务添加成功: {task_name}")
                print(f"   聊天: {chat_name} ({chat_type})")
                print(f"   时间: {send_datetime}")
                print(f"   消息: {message}")
            else:
                print(f"❌ 添加任务失败: {task_name}")
                
        except Exception as e:
            print(f"❌ 添加一次性任务失败: {e}")

    def list_tasks(self):
        """列出所有定时任务"""
        try:
            tasks = self.db.get_all_scheduled_messages()
            
            if not tasks:
                print("📋 暂无定时消息任务")
                return
            
            print(f"📋 共有 {len(tasks)} 个定时消息任务:")
            print("-" * 80)
            
            for task in tasks:
                status = "✅ 启用" if task['enabled'] else "❌ 禁用"
                print(f"任务名称: {task['task_name']} ({status})")
                print(f"聊天对象: {task['chat_name']} ({task['chat_type']})")
                print(f"调度类型: {task['schedule_type']}")
                
                if task['schedule_type'] == 'daily':
                    print(f"执行时间: 每天 {task['schedule_time']}")
                elif task['schedule_type'] == 'weekly':
                    weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
                    print(f"执行时间: 每{weekdays[task['schedule_weekday']]} {task['schedule_time']}")
                elif task['schedule_type'] == 'monthly':
                    print(f"执行时间: 每月{task['schedule_day']}日 {task['schedule_time']}")
                elif task['schedule_type'] == 'interval':
                    print(f"执行间隔: 每 {task['interval_minutes']} 分钟")
                elif task['schedule_type'] == 'once':
                    print(f"执行时间: {task['schedule_date']} {task['schedule_time']}")
                
                print(f"消息内容: {task['message_content']}")
                print(f"已发送: {task['send_count']} 次", end="")
                if task['max_send_count']:
                    print(f" / {task['max_send_count']} 次")
                else:
                    print(" (无限制)")
                
                if task['last_sent_at']:
                    print(f"最后发送: {task['last_sent_at']}")
                if task['next_send_at']:
                    print(f"下次发送: {task['next_send_at']}")
                
                print("-" * 80)
                
        except Exception as e:
            print(f"❌ 获取任务列表失败: {e}")

    def delete_task(self, task_name: str):
        """删除定时任务"""
        try:
            success = self.db.delete_scheduled_message(task_name)
            if success:
                print(f"✅ 任务删除成功: {task_name}")
            else:
                print(f"❌ 任务删除失败或任务不存在: {task_name}")
        except Exception as e:
            print(f"❌ 删除任务失败: {e}")

    def enable_task(self, task_name: str, enabled: bool = True):
        """启用/禁用任务"""
        try:
            success = self.db.update_scheduled_message(task_name, enabled=enabled)
            if success:
                status = "启用" if enabled else "禁用"
                print(f"✅ 任务{status}成功: {task_name}")
            else:
                print(f"❌ 任务状态更新失败或任务不存在: {task_name}")
        except Exception as e:
            print(f"❌ 更新任务状态失败: {e}")

    def show_history(self, task_name: str = None, limit: int = 20):
        """显示发送历史"""
        try:
            history = self.db.get_message_history(task_name, limit)
            
            if not history:
                if task_name:
                    print(f"📋 任务 '{task_name}' 暂无发送历史")
                else:
                    print("📋 暂无发送历史")
                return
            
            title = f"任务 '{task_name}' 的发送历史" if task_name else "所有任务的发送历史"
            print(f"📋 {title} (最近 {len(history)} 条):")
            print("-" * 80)
            
            for record in history:
                status_icon = "✅" if record['status'] == 'success' else "❌"
                print(f"{status_icon} {record['sent_at']} - {record['task_name']}")
                print(f"   聊天: {record['chat_name']} ({record['chat_type']})")
                print(f"   消息: {record['message_content']}")
                if record['error_message']:
                    print(f"   错误: {record['error_message']}")
                print("-" * 40)
                
        except Exception as e:
            print(f"❌ 获取发送历史失败: {e}")

    def close(self):
        """关闭数据库连接"""
        if self.db:
            self.db.close()


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("定时消息管理工具")
        print("=" * 50)
        print("用法:")
        print("  python scheduled_message_manager.py list                    # 列出所有任务")
        print("  python scheduled_message_manager.py add_daily <参数>        # 添加每日任务")
        print("  python scheduled_message_manager.py add_weekly <参数>       # 添加每周任务")
        print("  python scheduled_message_manager.py add_interval <参数>     # 添加间隔任务")
        print("  python scheduled_message_manager.py add_once <参数>         # 添加一次性任务")
        print("  python scheduled_message_manager.py delete <任务名>         # 删除任务")
        print("  python scheduled_message_manager.py enable <任务名>         # 启用任务")
        print("  python scheduled_message_manager.py disable <任务名>        # 禁用任务")
        print("  python scheduled_message_manager.py history [任务名]        # 查看发送历史")
        print()
        print("示例:")
        print('  python scheduled_message_manager.py add_daily "早安问候" "测试群" group "早上好！新的一天开始了！" "08:30"')
        print('  python scheduled_message_manager.py add_weekly "周报提醒" "工作群" group "请大家提交周报" 0 "17:00"')
        print('  python scheduled_message_manager.py add_interval "心跳检测" "监控群" group "系统正常运行中" 60')
        print('  python scheduled_message_manager.py add_once "会议提醒" "项目群" group "会议将在30分钟后开始" "2024-12-25 14:30"')
        return
    
    try:
        manager = ScheduledMessageManager()
        command = sys.argv[1].lower()
        
        if command == 'list':
            manager.list_tasks()
            
        elif command == 'add_daily':
            if len(sys.argv) != 7:
                print("❌ 参数错误")
                print("用法: add_daily <任务名> <聊天名> <聊天类型> <消息内容> <时间>")
                print("示例: add_daily \"早安问候\" \"测试群\" group \"早上好！\" \"08:30\"")
                return
            _, _, task_name, chat_name, chat_type, message, send_time = sys.argv
            manager.add_daily_task(task_name, chat_name, chat_type, message, send_time)
            
        elif command == 'add_weekly':
            if len(sys.argv) != 8:
                print("❌ 参数错误")
                print("用法: add_weekly <任务名> <聊天名> <聊天类型> <消息内容> <星期几> <时间>")
                print("示例: add_weekly \"周报提醒\" \"工作群\" group \"请提交周报\" 0 \"17:00\"")
                print("星期几: 0=周一, 1=周二, ..., 6=周日")
                return
            _, _, task_name, chat_name, chat_type, message, weekday, send_time = sys.argv
            manager.add_weekly_task(task_name, chat_name, chat_type, message, int(weekday), send_time)
            
        elif command == 'add_interval':
            if len(sys.argv) != 7:
                print("❌ 参数错误")
                print("用法: add_interval <任务名> <聊天名> <聊天类型> <消息内容> <间隔分钟>")
                print("示例: add_interval \"心跳检测\" \"监控群\" group \"系统正常\" 60")
                return
            _, _, task_name, chat_name, chat_type, message, interval = sys.argv
            manager.add_interval_task(task_name, chat_name, chat_type, message, int(interval))
            
        elif command == 'add_once':
            if len(sys.argv) != 7:
                print("❌ 参数错误")
                print("用法: add_once <任务名> <聊天名> <聊天类型> <消息内容> <时间>")
                print("示例: add_once \"会议提醒\" \"项目群\" group \"会议开始\" \"2024-12-25 14:30\"")
                return
            _, _, task_name, chat_name, chat_type, message, send_datetime = sys.argv
            manager.add_once_task(task_name, chat_name, chat_type, message, send_datetime)
            
        elif command == 'delete':
            if len(sys.argv) != 3:
                print("❌ 参数错误")
                print("用法: delete <任务名>")
                return
            task_name = sys.argv[2]
            manager.delete_task(task_name)
            
        elif command == 'enable':
            if len(sys.argv) != 3:
                print("❌ 参数错误")
                print("用法: enable <任务名>")
                return
            task_name = sys.argv[2]
            manager.enable_task(task_name, True)
            
        elif command == 'disable':
            if len(sys.argv) != 3:
                print("❌ 参数错误")
                print("用法: disable <任务名>")
                return
            task_name = sys.argv[2]
            manager.enable_task(task_name, False)
            
        elif command == 'history':
            task_name = sys.argv[2] if len(sys.argv) > 2 else None
            manager.show_history(task_name)
            
        else:
            print(f"❌ 未知命令: {command}")
            
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if 'manager' in locals():
            manager.close()


if __name__ == "__main__":
    main()
