#!/usr/bin/env python3
"""
测试DeepSeek JSON解析修复
"""

import json
from utils.deepseek_client import DeepSeekClient

def test_json_cleaning():
    """测试JSON清理功能"""
    print("🔍 测试JSON清理功能...")
    
    # 创建DeepSeek客户端实例（不需要真实API密钥来测试清理功能）
    client = DeepSeekClient("test_key")
    
    # 测试用例1：带有```json标记的响应
    test_response_1 = """```json
[
    {"tag_name": "问题类型", "tag_value": "咨询", "confidence": 0.9},
    {"tag_name": "情感倾向", "tag_value": "焦虑", "confidence": 0.95},
    {"tag_name": "用户类型", "tag_value": "普通用户", "confidence": 0.8}
]
```"""
    
    # 测试用例2：只有```标记的响应
    test_response_2 = """```
[
    {"tag_name": "紧急程度", "tag_value": "紧急", "confidence": 0.9},
    {"tag_name": "咨询时间", "tag_value": "非工作时间", "confidence": 0.7}
]
```"""
    
    # 测试用例3：没有标记的纯JSON
    test_response_3 = """[
    {"tag_name": "问题类型", "tag_value": "投诉", "confidence": 0.95}
]"""
    
    test_cases = [
        ("带有```json标记", test_response_1),
        ("只有```标记", test_response_2),
        ("纯JSON", test_response_3)
    ]
    
    all_passed = True
    
    for test_name, response in test_cases:
        print(f"\n📋 测试: {test_name}")
        print(f"原始响应: {repr(response)}")
        
        try:
            # 清理响应
            cleaned = client._clean_json_response(response)
            print(f"清理后: {repr(cleaned)}")
            
            # 尝试解析JSON
            parsed = json.loads(cleaned)
            print(f"解析结果: {parsed}")
            
            if isinstance(parsed, list) and len(parsed) > 0:
                print("✅ 测试通过")
            else:
                print("❌ 解析结果格式不正确")
                all_passed = False
                
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            all_passed = False
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            all_passed = False
    
    return all_passed


def test_edge_cases():
    """测试边界情况"""
    print("\n🔍 测试边界情况...")
    
    client = DeepSeekClient("test_key")
    
    edge_cases = [
        ("空字符串", ""),
        ("只有标记", "```json```"),
        ("多余空白", "  ```json  \n  []  \n  ```  "),
        ("嵌套标记", "```json\n```\n[]\n```\n```"),
        ("不完整标记", "```json\n[]"),
    ]
    
    for test_name, response in edge_cases:
        print(f"\n📋 测试边界情况: {test_name}")
        print(f"输入: {repr(response)}")
        
        try:
            cleaned = client._clean_json_response(response)
            print(f"清理后: {repr(cleaned)}")
            
            if cleaned:  # 只有非空字符串才尝试解析
                parsed = json.loads(cleaned)
                print(f"解析结果: {parsed}")
                print("✅ 边界情况处理正确")
            else:
                print("⚠️ 清理后为空字符串")
                
        except json.JSONDecodeError:
            print("⚠️ JSON解析失败（预期行为）")
        except Exception as e:
            print(f"❌ 意外异常: {e}")


def test_real_scenario():
    """测试真实场景"""
    print("\n🔍 测试真实场景...")
    
    # 模拟实际DeepSeek返回的响应
    real_response = """```json
[
    {"tag_name": "问题类型", "tag_value": "咨询", "confidence": 0.9},
    {"tag_name": "情感倾向", "tag_value": "焦虑", "confidence": 0.95},
    {"tag_name": "用户类型", "tag_value": "普通用户", "confidence": 0.8},
    {"tag_name": "紧急程度", "tag_value": "紧急", "confidence": 0.9},
    {"tag_name": "咨询时间", "tag_value": "非工作时间", "confidence": 0.7}
]
```"""
    
    client = DeepSeekClient("test_key")
    
    try:
        cleaned = client._clean_json_response(real_response)
        parsed = json.loads(cleaned)
        
        print(f"解析成功，共 {len(parsed)} 个标签:")
        for tag in parsed:
            tag_name = tag.get('tag_name', '未知')
            tag_value = tag.get('tag_value', '未知')
            confidence = tag.get('confidence', 0)
            print(f"  - {tag_name}: {tag_value} ({confidence:.1%})")
        
        print("✅ 真实场景测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 真实场景测试失败: {e}")
        return False


def main():
    """主函数"""
    print("=" * 60)
    print("DeepSeek JSON解析修复测试")
    print("=" * 60)
    
    tests = [
        ("JSON清理功能", test_json_cleaning),
        ("边界情况", test_edge_cases),
        ("真实场景", test_real_scenario),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func == test_edge_cases:
                test_func()  # 边界情况测试不返回结果
                results.append((test_name, True))
            else:
                result = test_func()
                results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！JSON解析问题已修复。")
        print("\n📝 修复说明:")
        print("- 添加了 _clean_json_response 方法来处理markdown代码块")
        print("- 支持移除 ```json 和 ``` 标记")
        print("- 处理多余的空白字符")
        print("- 现在可以正确解析DeepSeek返回的JSON响应")
    else:
        print("⚠️ 部分测试失败，需要进一步检查。")


if __name__ == '__main__':
    main()
