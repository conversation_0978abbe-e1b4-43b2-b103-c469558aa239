from typing import List, Dict
from wxautox import WeChat


def handle_private_message(wx: WeChat, chat: str, contents: List[str], messages: List[Dict], plugins, log_func):
    log_func(f"[私聊] 开始处理 '{chat}' 中的消息", 'DEBUG')

    # 首先进行黑名单检查 - 最高优先级
    for plugin in plugins:
        if plugin.__class__.__name__ == 'BlacklistPlugin':
            try:
                reply = plugin.on_messages(chat, contents)
                if reply == "__BLACKLISTED__":
                    log_func(f"[私聊] 聊天 '{chat}' 在黑名单中，终止处理流程", 'DEBUG')
                    return
            except Exception as e:
                log_func(f"[黑名单插件异常] {e}", 'ERROR')
            break

    # 然后尝试对话记录插件（如果存在）
    conversation_plugin = None
    for plugin in plugins:
        if plugin.__class__.__name__ == 'ConversationHistoryPlugin':
            conversation_plugin = plugin
            break

    if conversation_plugin:
        try:
            # 调用对话记录插件的专门方法处理用户消息
            reply = conversation_plugin.handle_user_messages(chat, 'private', messages)
            if reply:
                _send_private_reply(wx, chat, reply, is_keyword=False, log_func=log_func, plugins=plugins)
                return
        except Exception as e:
            log_func(f"[对话记录插件异常] {e}", 'ERROR')

    reply = None
    # 关键词插件优先匹配
    for plugin in plugins:
        try:
            # 跳过对话记录插件，避免重复处理
            if plugin.__class__.__name__ == 'ConversationHistoryPlugin':
                continue

            reply = plugin.on_messages(chat, contents)
            if reply == "__BLACKLISTED__":
                log_func(f"[私聊] 聊天 '{chat}' 在黑名单中，终止处理流程", 'DEBUG')
                return
            if reply:
                _send_private_reply(wx, chat, reply, is_keyword=True, log_func=log_func, plugins=plugins)
                return
        except Exception as e:
            log_func(f"[插件异常] {plugin.__class__.__name__}: {e}", 'ERROR')

    # AI 插件（无需 @）
    for plugin in plugins:
        try:
            # 跳过对话记录插件，避免重复处理
            if plugin.__class__.__name__ == 'ConversationHistoryPlugin':
                continue

            reply = plugin.on_messages(chat, contents)
            if reply == "__BLACKLISTED__":
                log_func(f"[私聊] 聊天 '{chat}' 在黑名单中，终止处理流程", 'DEBUG')
                return
            if reply:
                _send_private_reply(wx, chat, reply, is_keyword=False, log_func=log_func, plugins=plugins)
                break
        except Exception as e:
            log_func(f"[插件异常] {plugin.__class__.__name__}: {e}", 'ERROR')


def _send_private_reply(wx: WeChat, chat: str, reply: str, is_keyword: bool, log_func, plugins=None):
    tag = "【关键词回复】" if is_keyword else "【AIChatPlugin】"
    level = "INFO" if is_keyword else "DEBUG"

    # 查找分段回复插件
    segmentation_plugin = None
    if plugins:
        for plugin in plugins:
            if plugin.__class__.__name__ == 'MessageSegmentationPlugin':
                segmentation_plugin = plugin
                break

    try:
        # 确保回复内容是字符串格式
        reply = str(reply).strip()
        log_func(f"准备发送私聊消息: {reply[:100]}...", 'DEBUG')

        # 使用分段发送功能
        if segmentation_plugin:
            segmentation_plugin.send_segmented_message(wx, chat, reply, None)  # 私聊不需要@用户
        else:
            # 回退到原始发送方式
            wx.SendMsg(reply, who=chat)

        log_func(f"{tag} 已发送完成", level)
    except Exception as e:
        log_func(f"发送失败: {e}", 'ERROR')
