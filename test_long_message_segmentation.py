#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试超长消息的真正分段发送效果
"""

import sys
import os
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

class MockWeChat:
    """模拟微信发送接口"""
    
    def __init__(self):
        self.sent_messages = []
        self.message_count = 0
    
    def SendMsg(self, message, who=None):
        """模拟发送消息"""
        self.message_count += 1
        timestamp = time.strftime("%H:%M:%S")
        self.sent_messages.append({
            'id': self.message_count,
            'timestamp': timestamp,
            'chat': who,
            'message': message,
            'method': 'SendMsg'
        })
        print(f"📤 [{timestamp}] 第{self.message_count}条消息发送到 '{who}':")
        print(f"    内容: {message}")
        print()
    
    def SendTypingText(self, message, who=None):
        """模拟发送打字机消息"""
        self.message_count += 1
        timestamp = time.strftime("%H:%M:%S")
        self.sent_messages.append({
            'id': self.message_count,
            'timestamp': timestamp,
            'chat': who,
            'message': message,
            'method': 'SendTypingText'
        })
        print(f"📤 [{timestamp}] 第{self.message_count}条消息发送到 '{who}' (打字机效果):")
        print(f"    内容: {message}")
        print()

def test_long_message_segmentation():
    """测试超长消息分段"""
    print("🚀 测试超长消息的真正分段发送")
    print("=" * 80)
    
    # 创建模拟handler
    class MockHandler:
        def _log(self, message, level='INFO'):
            print(f"[{level}] {message}")
    
    # 创建模拟微信实例
    mock_wx = MockWeChat()
    
    # 导入插件
    from plugins.message_segmentation.plugin import MessageSegmentationPlugin
    
    # 创建插件实例
    handler = MockHandler()
    plugin = MessageSegmentationPlugin(handler)
    
    print(f"📋 当前配置:")
    print(f"   - 启用状态: {plugin.enabled}")
    print(f"   - 分段模式: {plugin.mode}")
    print(f"   - 字数阈值: {plugin.max_length}")
    print(f"   - 间隔时间: {plugin.segment_delay}秒")
    
    # 创建一个超长消息（超过500字）
    long_message = """
关于微信机器人的分段回复功能，我想详细说明一下实现原理和使用方法。首先，这个功能的核心目的是解决长消息发送时的用户体验问题。当AI生成的回复内容很长时，一次性发送可能会让用户感到信息过载，难以快速理解和消化。

通过分段发送机制，我们可以将长消息智能地分割成多个较短的段落，每个段落作为独立的消息发送出去。这样用户就能看到多条连续的消息，就像真人在逐条发送一样，显得更加自然和人性化。

分段算法会优先按照句子进行分割，以句号、问号、感叹号等标点符号为分界点。这样可以保持每个分段的语义完整性，避免在词语或句子中间断开，确保用户能够完整理解每个分段的含义。

在发送过程中，第一条消息会包含@用户的信息（如果是群聊的话），后续的消息则不会重复@，避免对用户造成过多的提醒干扰。同时，为了让用户知道还有后续内容，系统会在非最后一条消息的末尾添加"(续)"标识。

分段之间会有适当的时间间隔，默认是1.5秒，这样可以模拟真实的打字和发送过程，让对话显得更加自然。用户可以根据实际需求调整这个间隔时间。

这个功能支持多种配置模式：不分段模式适合希望保持消息完整性的场景；强制分段模式会对所有超过50字的消息进行分段；自动分段模式根据设定的字数阈值智能判断；概率分段模式则增加了一定的随机性，让回复更加多样化。

总的来说，分段回复功能既保证了消息内容的完整传达，又显著提升了用户的阅读体验，是微信机器人人性化交互的重要组成部分。
    """.strip()
    
    print(f"\n📝 测试超长消息:")
    print(f"   消息长度: {len(long_message)}字")
    print(f"   预期分段: {'是' if len(long_message) > plugin.max_length else '否'}")
    print(f"   消息预览: {long_message[:100]}...")
    
    print(f"\n🚀 开始分段发送...")
    print("=" * 80)
    
    # 记录开始时间
    start_time = time.time()
    
    # 调用分段发送
    plugin.send_segmented_message(
        wx=mock_wx,
        chat="技术讨论群",
        message=long_message,
        at_users=["张三", "李四", "王五"]
    )
    
    # 记录结束时间
    end_time = time.time()
    total_time = end_time - start_time
    
    print("=" * 80)
    print(f"📊 发送完成统计:")
    print(f"   - 总发送时间: {total_time:.1f}秒")
    print(f"   - 发送消息数: {mock_wx.message_count}条")
    print(f"   - 平均每条间隔: {total_time/max(1, mock_wx.message_count-1):.1f}秒")
    
    print(f"\n📋 消息分段效果:")
    for i, msg in enumerate(mock_wx.sent_messages, 1):
        print(f"   第{i}条 [{msg['timestamp']}]: {len(msg['message'])}字")
        print(f"        {msg['message'][:80]}...")
        if i < len(mock_wx.sent_messages):
            print(f"        [间隔等待...]")
        print()
    
    print("🎉 测试完成！可以看到长消息被分成了多条独立消息发送")

if __name__ == "__main__":
    test_long_message_segmentation()
