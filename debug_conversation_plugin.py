#!/usr/bin/env python3
"""
对话记录插件调试脚本
用于测试和调试对话记录插件的功能
"""

import os
import sys
import json
from datetime import datetime
from utils.config_utils import load_config
from db_plugins.mysql import MySQLDB


def test_plugin_response_format():
    """测试插件回复格式"""
    print("🧪 测试对话记录插件回复格式...")
    
    try:
        # 模拟handler
        class MockHandler:
            def __init__(self):
                cfg = load_config()
                db_config = cfg.get("mysql", {})
                from db_plugins.mysql import MySQLDB
                self.db = MySQLDB(**db_config)
                self.db.connect()
                self.chat_type_cache = {"测试群聊": "group"}
            
            def _log(self, message, level="INFO"):
                print(f"[{level}] {message}")
        
        # 创建插件实例
        from plugins.conversation_history.plugin import ConversationHistoryPlugin
        handler = MockHandler()
        plugin = ConversationHistoryPlugin(handler)
        
        print("✅ 插件实例化成功")
        
        # 模拟用户消息
        test_user_messages = [
            {"sender": "测试用户1", "content": "AI 你好", "at_me": False},
            {"sender": "测试用户2", "content": "AI 请帮我", "at_me": False}
        ]
        
        print("📝 测试用户消息:")
        for msg in test_user_messages:
            print(f"  - {msg['sender']}: {msg['content']}")
        
        # 测试插件处理
        result = plugin.handle_user_messages("测试群聊", "group", test_user_messages)
        
        print(f"\n📤 插件返回结果:")
        if result:
            print(f"类型: {type(result)}")
            print(f"长度: {len(result)}")
            print(f"内容: {repr(result)}")
            print(f"预览: {result[:200]}...")
        else:
            print("None (未配置AI智能体或其他原因)")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_message_format():
    """测试消息格式处理"""
    print("\n🔍 测试消息格式处理...")
    
    # 测试各种可能的消息格式
    test_messages = [
        "简单消息",
        "包含\n换行符的消息",
        "包含特殊字符的消息：@#$%^&*()",
        "很长的消息" + "x" * 1000,
        "",  # 空消息
        "   带空格的消息   ",
        "多行\n消息\n测试",
    ]
    
    for i, msg in enumerate(test_messages):
        print(f"测试消息 {i+1}:")
        print(f"  原始: {repr(msg)}")
        
        # 模拟清理过程
        cleaned = str(msg).strip()
        print(f"  清理后: {repr(cleaned)}")
        
        # 模拟发送格式
        if cleaned:
            send_format = f"{{@测试用户}}\n{cleaned}"
            print(f"  发送格式: {repr(send_format[:100])}")
        print()


def test_database_operations():
    """测试数据库操作"""
    print("🗄️ 测试数据库操作...")
    
    try:
        # 连接数据库
        cfg = load_config()
        db_config = cfg.get("mysql", {})
        db = MySQLDB(**db_config)
        db.connect()
        
        # 注册SQL函数
        from plugins.conversation_history.sql import CONVERSATION_HISTORY_SQL_FUNCTIONS
        db.register_plugin_functions("DebugTest", CONVERSATION_HISTORY_SQL_FUNCTIONS)
        
        # 测试保存消息
        print("💾 测试保存消息...")
        db.save_message(
            chat_name="调试测试群",
            chat_type="group",
            sender="调试用户",
            message_content="这是一条调试消息",
            message_type="user",
            session_hours=24
        )
        print("✅ 消息保存成功")
        
        # 测试查询上下文
        print("📖 测试查询上下文...")
        context = db.get_user_conversation_context("调试测试群", "group", "调试用户", 10, 24)
        print(f"✅ 查询到 {len(context)} 条上下文记录")
        
        # 清理测试数据
        cursor = db.connection.cursor()
        cursor.execute("DELETE FROM conversation_history WHERE chat_name = '调试测试群'")
        db.connection.commit()
        print("🧹 清理测试数据完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_send_message_simulation():
    """模拟发送消息测试"""
    print("\n📤 模拟发送消息测试...")
    
    # 模拟各种回复格式
    test_replies = [
        "简单回复",
        "包含@的回复\n{@用户名}\n这是回复内容",
        "多行回复\n第一行\n第二行\n第三行",
        "很长的回复" + "内容" * 100,
    ]
    
    for i, reply in enumerate(test_replies):
        print(f"\n测试回复 {i+1}:")
        print(f"原始回复: {repr(reply[:100])}...")
        
        # 模拟 _send_group_reply 的处理逻辑
        try:
            # 模拟消息处理
            unique_senders = {"测试用户1", "测试用户2"}
            enable_at_reply = True
            
            if enable_at_reply and unique_senders:
                at_part = ''.join([f"{{@{s}}}" for s in unique_senders])
                msg_text = f"{at_part}\n{reply}"
            else:
                msg_text = reply
            
            # 确保消息文本是字符串格式
            msg_text = str(msg_text).strip()
            
            print(f"处理后消息: {repr(msg_text[:100])}...")
            print(f"消息长度: {len(msg_text)}")
            print("✅ 消息格式处理成功")
            
        except Exception as e:
            print(f"❌ 消息格式处理失败: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    print("=" * 60)
    print("对话记录插件调试工具")
    print("=" * 60)
    
    # 测试插件回复格式
    if not test_plugin_response_format():
        print("❌ 插件回复格式测试失败")
        return
    
    # 测试消息格式
    test_message_format()
    
    # 测试数据库操作
    if not test_database_operations():
        print("❌ 数据库操作测试失败")
        return
    
    # 测试发送消息模拟
    test_send_message_simulation()
    
    print("\n" + "=" * 60)
    print("🎉 调试测试完成！")
    print("=" * 60)
    
    print("\n📝 调试建议:")
    print("1. 检查日志中的详细错误信息")
    print("2. 确认AI智能体配置是否正确")
    print("3. 验证数据库连接和表结构")
    print("4. 检查消息格式是否符合预期")


if __name__ == '__main__':
    main()
