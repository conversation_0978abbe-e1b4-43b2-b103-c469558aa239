#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试强制分段模式
"""

import sys
import os
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

class MockWeChat:
    """模拟微信发送接口"""
    
    def __init__(self):
        self.sent_messages = []
        self.message_count = 0
    
    def SendMsg(self, message, who=None):
        """模拟发送消息"""
        self.message_count += 1
        timestamp = time.strftime("%H:%M:%S")
        self.sent_messages.append({
            'id': self.message_count,
            'timestamp': timestamp,
            'chat': who,
            'message': message,
            'method': 'SendMsg'
        })
        print(f"📤 [{timestamp}] 第{self.message_count}条消息发送到 '{who}':")
        print(f"    {message}")
        print()
    
    def SendTypingText(self, message, who=None):
        """模拟发送打字机消息"""
        self.message_count += 1
        timestamp = time.strftime("%H:%M:%S")
        self.sent_messages.append({
            'id': self.message_count,
            'timestamp': timestamp,
            'chat': who,
            'message': message,
            'method': 'SendTypingText'
        })
        print(f"📤 [{timestamp}] 第{self.message_count}条消息发送到 '{who}' (打字机):")
        print(f"    {message}")
        print()

def test_force_segmentation():
    """测试强制分段模式"""
    print("🚀 测试强制分段模式")
    print("=" * 60)
    
    # 创建模拟handler
    class MockHandler:
        def _log(self, message, level='INFO'):
            print(f"[{level}] {message}")
    
    # 创建模拟微信实例
    mock_wx = MockWeChat()
    
    # 导入插件
    from plugins.message_segmentation.plugin import MessageSegmentationPlugin
    
    # 创建插件实例
    handler = MockHandler()
    plugin = MessageSegmentationPlugin(handler)
    
    print(f"📋 当前配置:")
    print(f"   - 启用状态: {plugin.enabled}")
    print(f"   - 分段模式: {plugin.mode}")
    print(f"   - 字数阈值: {plugin.max_length}")
    print(f"   - 间隔时间: {plugin.segment_delay}秒")
    
    # 测试消息（和你实际收到的一样）
    test_message = '''戏精附体版：  
"辣的！宝子这要求我太懂了（搓手手）海底捞新出的麻辣锅底配酥肉，辣到头皮发麻那种～咱直接安排上啊！🔥"

网络热梗版：  
"姐妹尊嘟要吃辣？建议狠狠点外卖来份变态辣炸鸡🍗绝绝子级别的辣味，CPU我都要燃烧起来了哈哈哈～"

温和婉转版：  
"想吃辣的啦？那咱们煮个简单的辣酱拌面吧～加点葱花和辣椒油，暖胃又过瘾（轻声）要不要试试看呢？"'''
    
    print(f"\n📝 测试消息:")
    print(f"   长度: {len(test_message)}字")
    print(f"   内容预览: {test_message[:50]}...")
    
    # 测试分段判断
    should_segment = plugin.should_segment(test_message)
    print(f"\n🔍 分段判断:")
    print(f"   需要分段: {should_segment}")
    print(f"   模式: {plugin.mode}")
    print(f"   判断逻辑: 强制模式下，长度{len(test_message)} > 50 = {len(test_message) > 50}")
    
    # 获取分段结果
    segments = plugin.segment_message(test_message)
    print(f"\n📄 分段结果:")
    print(f"   分段数量: {len(segments)}")
    
    for i, segment in enumerate(segments, 1):
        print(f"\n   段落{i} ({len(segment)}字):")
        print(f"   {segment}")
    
    print(f"\n🚀 开始模拟发送...")
    print("=" * 60)
    
    # 模拟发送
    plugin.send_segmented_message(
        wx=mock_wx,
        chat="守望大佬爱学习",
        message=test_message,
        at_users=["春面不觉晓"]
    )
    
    print("=" * 60)
    print(f"📊 发送统计:")
    print(f"   - 发送消息数: {mock_wx.message_count}条")
    print(f"   - 预期效果: 每条消息独立发送，有时间间隔")
    
    print(f"\n🎉 测试完成！")
    print("如果看到多条消息分别发送，说明分段功能正常工作")

if __name__ == "__main__":
    test_force_segmentation()
