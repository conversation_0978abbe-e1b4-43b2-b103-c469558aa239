from typing import List, Dict
from wxautox import WeChat


def handle_group_message(wx: WeChat, chat: str, contents: List[str], messages: List[Dict], plugins, log_func):
    log_func(f"[群聊] 开始处理 '{chat}' 中的消息", 'DEBUG')

    # 首先进行黑名单检查 - 最高优先级
    for plugin in plugins:
        if plugin.__class__.__name__ == 'BlacklistPlugin':
            try:
                reply = plugin.on_messages(chat, contents)
                if reply == "__BLACKLISTED__":
                    log_func(f"[群聊] 聊天 '{chat}' 在黑名单中，终止处理流程", 'DEBUG')
                    return
            except Exception as e:
                log_func(f"[黑名单插件异常] {e}", 'ERROR')
            break

    # 然后尝试对话记录插件（如果存在）
    conversation_plugin = None
    for plugin in plugins:
        if plugin.__class__.__name__ == 'ConversationHistoryPlugin':
            conversation_plugin = plugin
            break

    if conversation_plugin:
        try:
            # 调用对话记录插件的专门方法处理用户消息
            reply = conversation_plugin.handle_user_messages(chat, 'group', messages)
            if reply:
                _send_group_reply(wx, chat, messages, reply, is_keyword=False, log_func=log_func, plugins=plugins)
                return
        except Exception as e:
            log_func(f"[对话记录插件异常] {e}", 'ERROR')

    reply = None
    for plugin in plugins:
        try:
            # 跳过对话记录插件，避免重复处理
            if plugin.__class__.__name__ == 'ConversationHistoryPlugin':
                continue

            reply = plugin.on_messages(chat, contents)
            if reply == "__BLACKLISTED__":
                log_func(f"[群聊] 聊天 '{chat}' 在黑名单中，终止处理流程", 'DEBUG')
                return
            if reply:
                _send_group_reply(wx, chat, messages, reply, is_keyword=True, log_func=log_func, plugins=plugins)
                return
        except Exception as e:
            log_func(f"[插件异常] {plugin.__class__.__name__}: {e}", 'ERROR')

    valid_contents = [m['content'] for m in messages if m["at_me"]]
    if not valid_contents:
        log_func(f"[群聊] 用户未 @ 我，跳过处理 '{chat}'", 'DEBUG')
        return

    for plugin in plugins:
        try:
            # 跳过对话记录插件，避免重复处理
            if plugin.__class__.__name__ == 'ConversationHistoryPlugin':
                continue

            reply = plugin.on_messages(chat, valid_contents)
            if reply == "__BLACKLISTED__":
                log_func(f"[群聊] 聊天 '{chat}' 在黑名单中，终止处理流程", 'DEBUG')
                return
            if reply:
                _send_group_reply(wx, chat, messages, reply, is_keyword=False, log_func=log_func, plugins=plugins)
                break
        except Exception as e:
            log_func(f"[插件异常] {plugin.__class__.__name__}: {e}", 'ERROR')


def _send_group_reply(wx: WeChat, chat: str, messages: List[Dict], reply: str, is_keyword: bool, log_func, plugins=None):
    from utils.config_utils import load_config
    cfg = load_config()
    enable_at_reply = cfg.get("enable_at_reply", False)

    tag = "【关键词回复】" if is_keyword else "【AIChatPlugin】"
    level = "INFO" if is_keyword else "DEBUG"

    unique_senders = set(m['sender'] for m in messages)
    at_users = list(unique_senders) if enable_at_reply and unique_senders else None

    # 查找分段回复插件
    segmentation_plugin = None
    if plugins:
        for plugin in plugins:
            if plugin.__class__.__name__ == 'MessageSegmentationPlugin':
                segmentation_plugin = plugin
                break

    try:
        # 确保回复内容是字符串格式
        reply = str(reply).strip()
        log_func(f"准备发送消息: {reply[:100]}...", 'DEBUG')

        # 使用分段发送功能
        if segmentation_plugin:
            segmentation_plugin.send_segmented_message(wx, chat, reply, at_users)
        else:
            # 回退到原始发送方式
            _send_group_reply_fallback(wx, chat, reply, at_users, log_func)

        log_func(f"{tag} 已发送完成", level)
    except Exception as e:
        import traceback
        log_func(f"发送失败: {e}", 'ERROR')
        log_func(f"错误详情: {traceback.format_exc()}", 'ERROR')


def _send_group_reply_fallback(wx: WeChat, chat: str, reply: str, at_users: List[str], log_func):
    """
    回退的群聊发送方式（不使用分段功能）
    """
    # 构建消息文本
    if at_users:
        at_part = ''.join([f"{{@{user}}}" for user in at_users])
        msg_text = f"{at_part}\n{reply}"
    else:
        msg_text = reply

    # 尝试使用 SendTypingText，如果失败则使用 SendMsg
    try:
        try:
            wx.SendTypingText(msg_text, who=chat)
        except TypeError as te:
            log_func(f"SendTypingText 参数错误，尝试其他方式: {te}", 'DEBUG')
            wx.SendTypingText(chat, msg_text)
    except Exception as typing_error:
        log_func(f"SendTypingText 失败，尝试使用 SendMsg: {typing_error}", 'WARNING')
        wx.SendMsg(msg_text, who=chat)
