import json

import requests
from typing import Dict, Any

from models import AIModel


class DifyChatFlowModel(AIModel):
    def generate(self, context: str, prompt_template: str = None) -> str:
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        payload = {
            "inputs": {},
            "query": context,
            "response_mode": "streaming",
            # "conversation_id": "test123",
            "user": "terminal_user"
        }

        final_answer = ""
        try:
            with requests.post(self.url, headers=headers, json=payload, stream=True, timeout=60) as response:
                if response.status_code == 200:
                    for line in response.iter_lines():
                        if line:
                            decoded_line = line.decode('utf-8')
                            if decoded_line.startswith("data:"):
                                try:
                                    json_data = json.loads(decoded_line[5:].strip())
                                    if json_data.get("event") == "message" and "answer" in json_data:
                                        chunk = json_data["answer"]
                                        final_answer += chunk
                                except Exception as e:
                                    continue
                else:
                    return "\nDify 接口请求失败，请检查网络或API状态。"
        except Exception as e:
            return f"\n发生错误：{e}"

        return final_answer