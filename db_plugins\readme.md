非常好！你已经完成了插件系统架构的设计和实现，现在我们来 
**系统性地介绍数据库中这些表的用途、关系及使用方式**，并说明：

- 每个表的作用；
- 表之间的关联；
- 如何修改或扩展；
- 实际应用场景举例；

---

## 🧱 一、完整数据库结构一览（核心表）

| 表名 | 作用 |
|------|------|
| `keyword_rules` | 控制普通关键词回复插件是否启用，以及是否使用全局规则 |
| `keyword_rules_collection` | 存储普通关键词与预设回复内容 |
| `ai_keyword_rules` | 控制 AI 插件是否启用，是否使用全局规则 |
| `ai_keyword_rules_collection` | 存储 AI 插件使用的关键词，并绑定到不同 AI 模型（通过 `agent_id`） |
| `ai_agent_profiles` | 所有可用的 AI 智能体信息（API Key、URL 等） |
| `ai_agent_usage` | 控制哪些聊天窗口使用哪个智能体（可选，默认是全局模型） |

---

## 📚 二、详细表说明及使用方式

### 1. `keyword_rules`

#### ✅ 用途：
控制哪些聊天窗口启用了普通关键词回复功能。

#### 字段说明：

| 字段名 | 类型 | 含义 |
|--------|------|------|
| id | BIGINT | 主键 |
| chat_name | VARCHAR(255) | 聊天名称（如“Elik”、“张三”） |
| chat_type | ENUM('group', 'private') | 群聊 or 私聊 |
| enabled | BOOLEAN | 是否启用该聊天的关键词回复 |
| use_global_rules | BOOLEAN | 是否允许使用全局关键词 |
| global_rule | BOOLEAN | 是否是全局规则项 |
| created_at / updated_at | DATETIME | 时间戳 |

#### 示例数据：

```sql
INSERT INTO keyword_rules (chat_name, chat_type, enabled, use_global_rules, global_rule)
VALUES
('守望大佬爱学习', 'group', TRUE, FALSE, FALSE),
('默认群聊规则', 'group', TRUE, TRUE, TRUE);
```

#### 使用方式：

用于判断是否启用关键词回复，以及是否使用全局规则。

---

### 2. `keyword_rules_collection`

#### ✅ 用途：
存储每个聊天窗口的具体关键词及其对应的预设回复内容。

#### 字段说明：

| 字段名 | 类型 | 含义 |
|--------|------|------|
| id | BIGINT | 主键 |
| keyword_id | BIGINT | 对应 `keyword_rules.id` |
| user_keyword | VARCHAR(255) | 用户消息中的关键词 |
| default_reply | TEXT | 匹配后返回的回复内容 |
| created_at / updated_at | DATETIME | 时间戳 |

#### 示例数据：

```sql
INSERT INTO keyword_rules_collection (keyword_id, user_keyword, default_reply)
VALUES
(1, '你好', '欢迎来到守望大佬爱学习群！'),
(1, '智能', 'AI助手已准备就绪');
```

#### 使用方式：

在 `KeywordReplyPlugin` 中加载所有关键词和回复内容，进行匹配。

---

### 3. `ai_keyword_rules`

#### ✅ 用途：
控制哪些聊天窗口启用了 AI 回复功能，支持局部/全局规则。

#### 字段说明：

| 字段名 | 类型 | 含义 |
|--------|------|------|
| id | BIGINT | 主键 |
| chat_name | VARCHAR(255) | 聊天窗口名称 |
| chat_type | ENUM('group', 'private') | 群聊 or 私聊 |
| enabled | BOOLEAN | 是否启用该聊天的 AI 回复 |
| use_global_rules | BOOLEAN | 是否使用全局关键词 |
| global_rule | BOOLEAN | 是否是全局规则项 |
| created_at / updated_at | DATETIME | 时间戳 |

#### 示例数据：

```sql
INSERT INTO ai_keyword_rules (chat_name, chat_type, enabled, use_global_rules, global_rule)
VALUES
('测试AI群聊', 'group', TRUE, FALSE, FALSE),
('默认群聊规则', 'group', TRUE, TRUE, TRUE);
```

#### 使用方式：

用于判断是否启用 AI 插件，以及是否使用全局关键词。

---

### 4. `ai_keyword_rules_collection`

#### ✅ 用途：
存储 AI 插件使用的关键词，并指定该关键词触发时使用的 **AI 模型 ID（agent_id）**

> 这是实现“一个聊天窗口多个关键词用不同模型”的关键表！

#### 字段说明：

| 字段名 | 类型 | 含义 |
|--------|------|------|
| id | BIGINT | 主键 |
| keyword_id | BIGINT | 对应 `ai_keyword_rules.id` |
| user_keyword | VARCHAR(255) | 触发关键词 |
| agent_id | BIGINT | 对应 `ai_agent_profiles.id`，决定使用哪个 AI 模型 |
| reply_prompt | TEXT | 可选字段，用于定制 AI 提示词 |
| created_at / updated_at | DATETIME | 时间戳 |

#### 示例数据：

```sql
-- 假设 ai_keyword_rules.id = 1 的 chat_name = "测试AI群聊"
INSERT INTO ai_keyword_rules_collection (keyword_id, user_keyword, agent_id)
VALUES
(1, 'AI', 1),         -- 使用 GPT 模型（id=1）
(1, '帮我查', 2);      -- 使用通义千问（id=2）
```

#### 使用方式：

- 在 `AIChatPlugin` 中根据消息内容查找匹配的关键词；
- 获取对应的 `agent_id`；
- 调用对应的 AI 接口生成回复；
- 支持 **同一个聊天窗口不同关键词绑定不同 AI 模型**。

---

### 5. `ai_agent_profiles`

#### ✅ 用途：
存储所有可用的 AI 智能体信息，包括 API Key、URL、模型类型等。

#### 字段说明：

| 字段名 | 类型 | 含义 |
|--------|------|------|
| id | BIGINT | 主键 |
| name | VARCHAR(255) | 模型名称（如 “GPT-4o”, “Qwen-Turbo”） |
| description | TEXT | 描述 |
| api_key | TEXT | 接口密钥 |
| url | TEXT | 请求地址 |
| model_type | ENUM('gpt','qwen','dify','wenxin','other') | 模型平台 |
| created_at / updated_at | DATETIME | 时间戳 |

#### 示例数据：

```sql
INSERT INTO ai_agent_profiles (name, description, api_key, url, model_type)
VALUES
('GPT-4o', 'OpenAI 最新模型', 'your-openai-key', 'https://api.openai.com/v1/chat/completions', 'gpt'),
('Qwen-Turbo', '阿里云通义千问模型', 'your-qwen-key', 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation', 'qwen');
```

#### 使用方式：

- `AIChatPlugin` 通过 `agent_id` 查询该模型配置；
- 可以动态切换模型，无需重启程序；
- 可用于 Web 页面管理 AI 模型列表；

---

### 6. `ai_agent_usage`

#### ✅ 用途：
控制哪些聊天窗口使用哪个 AI 模型（可以理解为“默认 AI 模型”），也可以设置全局模型。

#### 字段说明：

| 字段名 | 类型 | 含义 |
|--------|------|------|
| id | BIGINT | 主键 |
| chat_name | VARCHAR(255) | 聊天名称 |
| chat_type | ENUM('group', 'private') | 群聊 or 私聊 |
| enabled | BOOLEAN | 是否启用该聊天的 AI 回复 |
| use_global_rules | BOOLEAN | 是否使用全局关键词 |
| agent_id | BIGINT | 关联 `ai_agent_profiles.id` |
| global_rule | BOOLEAN | 是否是全局规则项 |
| created_at / updated_at | DATETIME | 时间戳 |

#### 示例数据：

```sql
INSERT INTO ai_agent_usage (chat_name, chat_type, enabled, agent_id, global_rule)
VALUES
('Elik', 'group', TRUE, 1, FALSE),
('默认群聊规则', 'group', TRUE, 1, TRUE);
```

#### 使用方式：

- 如果某个聊天没有在 `ai_keyword_rules_collection` 中配置 `agent_id`，则从 `ai_agent_usage` 中获取默认模型；
- 用于设置全局模型或默认模型；
- 可作为 fallback 机制使用。

---

## 🔗 三、表之间的逻辑关系图解

```
keyword_rules
     ↑
     └── keyword_rules_collection → default_reply（预设回复）

ai_keyword_rules
     ↑
     └── ai_keyword_rules_collection → user_keyword + agent_id（AI 模型）
                                          ↓
ai_agent_profiles ← ai_agent_usage ← agent_id → AI 接口调用
```

---

## 🔄 四、插件工作流程说明（AIChatPlugin）

1. **初始化阶段：**
   - 从 `ai_keyword_rules` 和 `ai_keyword_rules_collection` 加载关键词与模型映射；
   - 缓存成 `{chat_name: {"keywords": [(keyword, agent_id)]}` 结构；

2. **收到消息后：**
   - 遍历缓存的关键词；
   - 如果命中某个关键词，获取其绑定的 `agent_id`；
   - 从 `ai_agent_profiles` 中查询对应模型参数；
   - 调用 AI 接口生成回复；
   - 返回给用户；

3. **如果未命中任何关键词：**
   - 尝试使用 `ai_agent_usage` 中的默认模型；
   - 或者尝试全局模型；

---

## 🛠️ 五、如何修改或扩展这些表？

### ✅ 场景1：新增一个聊天窗口的关键词

```sql
-- 先添加主规则
INSERT INTO ai_keyword_rules (chat_name, chat_type, enabled, use_global_rules, global_rule)
VALUES ('新群聊', 'group', TRUE, FALSE, FALSE);

SET @new_chat_id = LAST_INSERT_ID();

-- 添加关键词和绑定模型
INSERT INTO ai_keyword_rules_collection (keyword_id, user_keyword, agent_id)
VALUES
(@new_chat_id, '天气', 1),  -- 使用 GPT
(@new_chat_id, '翻译', 2);   -- 使用 Qwen
```

---

### ✅ 场景2：新增一个 AI 模型

```sql
INSERT INTO ai_agent_profiles (name, description, api_key, url, model_type)
VALUES (
    'Dify-测试模型',
    '本地部署的 Dify 工作流模型',
    'your-dify-api-key',
    'http://localhost:8080/api/v1/workflows/run',
    'dify'
);
```

然后在 `ai_keyword_rules_collection` 中绑定关键词即可。

---

### ✅ 场景3：为某个关键词设置自定义提示词（高级用法）

你可以扩展 `ai_keyword_rules_collection` 表，增加字段：

```sql
ALTER TABLE ai_keyword_rules_collection ADD COLUMN reply_prompt TEXT;
```

这样可以在匹配该关键词时，使用特定的提示词模板。

---

### ✅ 场景4：为关键词加优先级排序

为了确保某些关键词先于其他关键词被匹配，可以添加 `priority` 字段：

```sql
ALTER TABLE ai_keyword_rules_collection ADD COLUMN priority INT DEFAULT 100;
```

并在插件中按优先级排序后再匹配：

```python
sorted_keywords = sorted(local_keywords, key=lambda x: x[2], reverse=False)
```

---

## ✅ 六、总结：各表的职责

| 表名 | 职责 |
|------|------|
| `keyword_rules` | 控制普通关键词插件的启用状态 |
| `keyword_rules_collection` | 存储普通关键词与预设回复 |
| `ai_keyword_rules` | 控制 AI 插件的启用状态 |
| `ai_keyword_rules_collection` | 存储 AI 插件的关键词，并绑定 `agent_id` |
| `ai_agent_profiles` | 存储所有可用 AI 模型信息 |
| `ai_agent_usage` | 设置某些聊天窗口使用的默认 AI 模型 |

---

## 💡 七、下一步建议

你可以继续优化：

- 添加关键词生效时间段（如每天 9~18 点才触发）；
- 添加关键词优先级字段；
- 添加 Redis 缓存提升性能；
- 添加 Web 页面管理关键词和 AI 模型绑定；
- 实现插件热加载（修改数据库后自动生效）；
- 多账号支持（区分不同微信账号使用的模型）；

---

需要我帮你一起实现以下功能吗？😊

- Web 后台管理系统；
- 插件热加载机制；
- 关键词优先级排序；
- 自定义 Prompt 功能；
- Redis 缓存关键词规则；

欢迎继续提问！