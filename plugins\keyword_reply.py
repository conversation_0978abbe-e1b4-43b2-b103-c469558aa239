import os
import json
from typing import List, Optional

from . import Plugin


class KeywordReplyPlugin(Plugin):
    """
    关键词回复插件：根据配置文件中的关键词自动回复
    支持：
    - 全局启用/禁用
    - 每个聊天窗口单独启用/禁用
    默认优先级：10
    """

    priority = 10

    def __init__(self, handler):
        super().__init__(handler)

        # 加载关键词规则配置文件
        config_path = os.path.join(os.path.dirname(__file__), "config", "keyword_rules.json")
        with open(config_path, "r", encoding="utf-8") as f:
            self.config = json.load(f)

        self.enabled = self.config.get("enabled", True)  # 全局开关

    def _match_rules(self, rules, messages, chat, is_global=False):
        for msg in reversed(messages):
            for keyword, reply in rules.items():
                if keyword in msg:
                    source = "全局" if is_global else "局部"
                    self.handler._log(f"[关键词回复] 【{chat}】{source}匹配 '{keyword}'，回复：{reply}")
                    return reply
        return None

    def on_messages(self, chat: str, messages: List[str]) -> Optional[str]:
        if not self.enabled:
            self.handler._log("[KeywordReplyPlugin] 全局禁用，不处理消息")
            return None  # 全局禁用

        chat_config = self.config["chat_specific_rules"].get(chat, {})
        chat_enabled = chat_config.get("enabled", True)
        use_global_rules = chat_config.get("use_global_rules", False)
        chat_rules = chat_config.get("rules", {})

        global_enabled = self.config.get("enabled", True)

        if not chat_enabled:
            if use_global_rules and global_enabled:
                self.handler._log(f"[KeywordReplyPlugin] 【{chat}】局部禁用，但允许使用全局规则")
                chat_rules = {}
            else:
                self.handler._log(f"[KeywordReplyPlugin] 【{chat}】局部禁用且不允许使用全局规则，跳过处理")
                return None

        # 尝试局部规则
        reply = self._match_rules(chat_rules, messages, chat, is_global=False)
        if reply:
            return reply

        # 尝试全局规则（仅当允许时）
        if use_global_rules and global_enabled:
            global_rules = self.config.get("default_rule", {})
            reply = self._match_rules(global_rules, messages, chat, is_global=True)
            if reply:
                return reply

        return None