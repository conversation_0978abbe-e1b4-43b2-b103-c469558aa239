# 对话记录插件使用说明

## 概述

对话记录插件是一个强大的微信机器人插件，具有以下功能：

1. **自动存储对话记录**：将所有用户消息和AI回复存储到数据库，记录发送者信息
2. **智能上下文查询**：查询最近100条（可配置）对话作为AI回复的上下文
3. **用户独立会话**：为不同用户维护独立的对话上下文和会话记录
4. **会话管理**：以1天（可配置）为一个会话周期，自动管理对话上下文
5. **关键词触发**：支持配置触发关键词，只有包含关键词的消息才会触发AI回复
6. **多AI模型支持**：支持配置不同的AI智能体进行回复
7. **用户区分回复**：AI回复时会@对应的用户，实现精准回复

## 功能特性

### 🗄️ 数据存储
- 存储所有用户消息和AI回复
- 记录发送者、时间戳、消息类型等详细信息
- 支持群聊和私聊的分别存储
- 为每个用户维护独立的消息记录

### 🧠 智能上下文
- 自动查询历史对话作为上下文
- **按用户区分上下文**：每个用户的AI回复基于该用户的历史对话
- 可配置上下文条数（默认100条）
- 基于会话周期管理上下文（默认24小时）

### 🤖 AI集成
- 支持多种AI模型（GPT、Dify等）
- 可为不同聊天配置不同的AI智能体
- 支持关键词触发机制
- **精准回复**：AI回复时自动@对应的用户

### ⚙️ 灵活配置
- 每个聊天窗口独立配置
- 可启用/禁用对话记录功能
- 可自定义触发关键词
- 可调整会话时长和上下文条数

### 👥 用户管理
- 记录每个用户的发言历史
- 支持查看聊天中的用户列表
- 支持查看特定用户的对话记录
- 为不同用户维护独立的AI对话上下文

## 安装和配置

### 1. 运行测试脚本

首先运行测试脚本确保插件正常工作：

```bash
python test_conversation_plugin.py
```

### 2. 配置AI智能体

在数据库的 `ai_agent_profiles` 表中添加AI智能体配置：

```sql
INSERT INTO ai_agent_profiles (name, description, api_key, url, model_type)
VALUES (
    '对话助手',
    '用于对话记录的AI助手',
    'your-api-key',
    'your-api-url',
    'gpt-3.5-turbo'
);
```

### 3. 配置聊天窗口

使用配置管理工具配置聊天窗口：

```bash
# 查看所有配置
python conversation_config_manager.py list

# 查看可用的AI智能体
python conversation_config_manager.py agents

# 为群聊添加配置
python conversation_config_manager.py add "你的群聊名称"

# 查看对话历史
python conversation_config_manager.py history "你的群聊名称"

# 查看用户列表
python conversation_config_manager.py users "你的群聊名称"

# 查看特定用户的对话记录
python conversation_config_manager.py user-history "你的群聊名称" "用户名"
```

## 数据库表结构

### conversation_history（对话记录表）
- `id`: 主键
- `chat_name`: 聊天窗口名称
- `chat_type`: 聊天类型（group/private）
- `sender`: 发送者
- `message_content`: 消息内容
- `message_type`: 消息类型（user/bot）
- `session_id`: 会话ID
- `created_at`: 创建时间

### conversation_config（对话配置表）
- `id`: 主键
- `chat_name`: 聊天窗口名称
- `chat_type`: 聊天类型
- `enabled`: 是否启用
- `context_limit`: 上下文条数限制
- `session_hours`: 会话时长（小时）
- `ai_agent_id`: AI智能体ID
- `trigger_keywords`: 触发关键词（JSON格式）

## 使用示例

### 基本使用

1. **启动机器人**：插件会自动加载并初始化数据库表
2. **发送消息**：用户在群聊中发送消息，插件自动存储并记录发送者
3. **触发AI**：发送包含触发关键词的消息（如"AI"、"助手"）
4. **获得回复**：AI基于该用户的历史上下文生成回复，并@该用户

### 配置示例

```bash
# 为"技术讨论群"配置对话记录
python conversation_config_manager.py add "技术讨论群" \
  --type group \
  --agent-id 1 \
  --context-limit 50 \
  --session-hours 12
```

### 用户区分功能

**场景示例**：
- 张三发送：`"AI 什么是Python？"`
- 李四发送：`"AI 什么是Java？"`

**插件处理**：
1. 分别保存张三和李四的消息，记录发送者
2. 为张三查询他的历史对话上下文
3. 为李四查询他的历史对话上下文
4. 分别生成针对性的回复
5. 回复格式：`{@张三}\n关于Python的回复...` 和 `{@李四}\n关于Java的回复...`

### 触发关键词

默认触发关键词：`["AI", "ai", "助手", "机器人"]`

**单用户触发示例**：
用户张三发送：`"AI 请帮我解释一下这个问题"`
插件会：
1. 保存张三的消息
2. 查询张三的历史对话上下文
3. 调用AI生成针对张三的回复
4. 保存AI回复
5. 返回：`{@张三}\n基于你之前的对话，这个问题...`

**多用户同时触发示例**：
- 张三：`"AI 帮我看看这个代码"`
- 李四：`"AI 这个错误怎么解决"`

插件会分别为每个用户生成回复：
```
{@张三}
根据你之前提到的项目，这段代码...

{@李四}
关于你遇到的错误，建议你...
```

## 高级功能

### 会话管理

插件使用会话ID来管理对话上下文：
- 会话ID基于聊天名称和时间窗口生成
- 默认24小时为一个会话周期
- 超过会话周期的消息不会作为上下文

### 数据清理

定期清理旧的对话记录：

```bash
# 清理30天前的对话记录
python conversation_config_manager.py cleanup 30
```

### 上下文构建

插件会自动构建包含历史对话的提示词：

```
=== 历史对话上下文 ===
用户(张三): 你好
助手: 你好！有什么可以帮助你的吗？
用户(李四): 请问今天天气如何？
=== 当前对话 ===
用户: AI 请帮我总结一下刚才的对话

请基于以上对话历史，给出合适的回复：
```

## 注意事项

1. **插件优先级**：插件优先级为200，会在其他插件之后执行
2. **数据存储**：所有消息都会被存储，请注意数据隐私
3. **性能考虑**：大量历史数据可能影响查询性能，建议定期清理
4. **AI配置**：需要正确配置AI智能体才能获得AI回复

## 故障排除

### 常见问题

1. **插件未加载**
   - 检查插件目录结构是否正确
   - 查看启动日志中的插件加载信息

2. **数据库连接失败**
   - 检查 `config.json` 中的数据库配置
   - 确保数据库服务正常运行

3. **AI回复失败**
   - 检查AI智能体配置是否正确
   - 查看日志中的错误信息
   - 验证API密钥和URL

4. **触发关键词不生效**
   - 检查配置中的触发关键词设置
   - 确保消息包含配置的关键词

### 调试方法

1. **查看日志**：观察机器人运行日志中的插件信息
2. **运行测试**：使用 `test_conversation_plugin.py` 测试基础功能
3. **检查数据库**：直接查询数据库表确认数据存储情况
4. **配置验证**：使用配置管理工具检查配置是否正确

## 更新日志

### v1.0.0
- 初始版本发布
- 支持对话记录存储
- 支持上下文查询
- 支持AI回复集成
- 支持配置管理
