# 人工服务工作时间功能说明

## 🎯 功能概述

人工服务插件现已支持工作时间控制功能，可以根据不同的时间段（工作时间、非工作时间、周末、节假日）返回不同的响应消息，让用户清楚了解客服的工作安排。

## ✨ 主要特性

- ✅ **灵活的工作时间设置**：支持自定义工作日和工作时间
- ✅ **智能时间判断**：自动识别工作时间、非工作时间、周末、节假日
- ✅ **个性化响应消息**：不同时间段返回不同的提示消息
- ✅ **节假日模式**：支持手动开启节假日模式
- ✅ **可视化管理工具**：提供命令行工具方便管理配置
- ✅ **实时状态显示**：显示当前时间状态和配置信息

## 📋 配置说明

### 配置文件结构

在 `config.json` 中的配置结构：

```json
{
  "human_handover": {
    "enabled": true,
    "work_schedule": {
      "enabled": true,
      "work_days": [1, 2, 3, 4, 5],
      "work_hours": {
        "start": "09:00",
        "end": "18:00"
      },
      "timezone": "Asia/Shanghai",
      "holiday_mode": false
    },
    "responses": {
      "work_hours": "您好！我已为您转接人工客服，客服人员会尽快为您处理。",
      "non_work_hours": "您好！现在是非工作时间（工作时间：周一至周五 09:00-18:00），您的问题已记录，客服人员会在工作时间内优先为您处理。如有紧急问题，请留下详细描述。",
      "weekend": "您好！现在是周末时间，您的问题已记录，客服人员会在下个工作日优先为您处理。如有紧急问题，请留下详细描述。",
      "holiday": "您好！现在是节假日期间，您的问题已记录，客服人员会在工作日恢复后优先为您处理。感谢您的理解！"
    }
  }
}
```

### 配置参数说明

#### work_schedule 工作时间安排

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enabled` | boolean | true | 是否启用工作时间功能 |
| `work_days` | array | [1,2,3,4,5] | 工作日（1=周一，7=周日） |
| `work_hours.start` | string | "09:00" | 工作开始时间 |
| `work_hours.end` | string | "18:00" | 工作结束时间 |
| `timezone` | string | "Asia/Shanghai" | 时区设置 |
| `holiday_mode` | boolean | false | 节假日模式 |

#### responses 响应消息

| 参数 | 说明 |
|------|------|
| `work_hours` | 工作时间内的响应消息 |
| `non_work_hours` | 非工作时间的响应消息 |
| `weekend` | 周末时间的响应消息 |
| `holiday` | 节假日期间的响应消息 |

## 🛠️ 管理工具使用

### 查看当前配置

```bash
python work_schedule_manager.py show
```

显示当前的工作时间配置、响应消息和实时状态。

### 设置工作时间

```bash
# 设置工作时间为 9:00-18:00
python work_schedule_manager.py set_hours 09:00 18:00

# 设置工作时间为 8:30-17:30
python work_schedule_manager.py set_hours 08:30 17:30
```

### 设置工作日

```bash
# 设置为周一到周五
python work_schedule_manager.py set_days weekdays

# 设置为周一到周六
python work_schedule_manager.py set_days 1,2,3,4,5,6

# 设置为全周工作
python work_schedule_manager.py set_days all
```

### 节假日模式管理

```bash
# 启用节假日模式
python work_schedule_manager.py holiday_on

# 禁用节假日模式
python work_schedule_manager.py holiday_off
```

### 设置响应消息

```bash
# 设置工作时间响应消息
python work_schedule_manager.py set_message work_hours "您好！客服在线，马上为您处理。"

# 设置非工作时间响应消息
python work_schedule_manager.py set_message non_work_hours "现在是下班时间，明天上班后会优先处理您的问题。"

# 设置周末响应消息
python work_schedule_manager.py set_message weekend "周末愉快！您的问题已记录，周一会及时处理。"

# 设置节假日响应消息
python work_schedule_manager.py set_message holiday "节假日期间暂停服务，假期结束后会尽快处理。"
```

## 🕐 时间判断逻辑

### 判断优先级

1. **节假日模式** - 如果启用，直接返回节假日响应
2. **工作日判断** - 检查当前是否为设定的工作日
3. **工作时间判断** - 检查当前时间是否在工作时间范围内

### 时间类型

| 类型 | 条件 | 响应消息类型 |
|------|------|-------------|
| `holiday` | 节假日模式启用 | holiday |
| `weekend` | 非工作日 | weekend |
| `work_hours` | 工作日 + 工作时间内 | work_hours |
| `non_work_hours` | 工作日 + 工作时间外 | non_work_hours |

## 📊 使用场景示例

### 场景1：标准工作时间

**配置：**
- 工作日：周一到周五
- 工作时间：09:00-18:00
- 节假日模式：关闭

**效果：**
- 周一到周五 09:00-18:00：显示工作时间消息
- 周一到周五 18:01-08:59：显示非工作时间消息
- 周六、周日：显示周末时间消息

### 场景2：客服中心模式

**配置：**
- 工作日：周一到周六
- 工作时间：08:30-20:00
- 节假日模式：根据需要开启

**效果：**
- 周一到周六 08:30-20:00：显示工作时间消息
- 其他时间：显示相应的非工作时间消息

### 场景3：节假日期间

**配置：**
- 节假日模式：开启

**效果：**
- 任何时间：都显示节假日消息

## 🔧 高级功能

### 自动标签生成

系统会根据用户咨询的时间自动生成标签：

- **工作时间**：标记为"工作时间咨询"
- **非工作时间**：标记为"非工作时间咨询"
- **周末时间**：标记为"周末时间咨询"
- **节假日时间**：标记为"节假日时间咨询"

### 处理建议

系统会根据咨询时间提供不同的处理建议：

- 非工作时间咨询：建议优先处理
- 周末咨询：可能较为紧急
- 节假日咨询：假期后优先处理

## ⚠️ 注意事项

1. **配置生效**：修改配置后需要重启机器人程序
2. **时间格式**：工作时间必须使用 HH:MM 格式（24小时制）
3. **工作日编号**：1=周一，2=周二，...，7=周日
4. **节假日模式**：启用后会覆盖所有其他时间判断

## 🚀 快速开始

1. **查看当前配置**
   ```bash
   python work_schedule_manager.py show
   ```

2. **设置基本工作时间**
   ```bash
   python work_schedule_manager.py set_hours 09:00 18:00
   python work_schedule_manager.py set_days weekdays
   ```

3. **重启机器人程序**
   ```bash
   python main.py
   ```

4. **测试功能**
   - 在私聊中发送"转人工"等关键词
   - 观察返回的响应消息是否符合当前时间状态

## 📈 效果展示

### 工作时间内
```
用户: 转人工
机器人: 您好！我已为您转接人工客服，客服人员会尽快为您处理。
```

### 非工作时间
```
用户: 转人工
机器人: 您好！现在是非工作时间（工作时间：周一至周五 09:00-18:00），您的问题已记录，客服人员会在工作时间内优先为您处理。如有紧急问题，请留下详细描述。
```

### 周末时间
```
用户: 转人工
机器人: 您好！现在是周末时间，您的问题已记录，客服人员会在下个工作日优先为您处理。如有紧急问题，请留下详细描述。
```

这样的设计让用户清楚了解客服的工作安排，提升了用户体验和服务质量！
