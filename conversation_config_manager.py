#!/usr/bin/env python3
"""
对话记录配置管理工具
用于管理对话记录插件的相关配置
"""

import os
import json
import sys
from datetime import datetime
from utils.config_utils import load_config
from db_plugins.mysql import MySQLDB


class ConversationConfigManager:
    """对话记录配置管理器"""

    def __init__(self):
        # 加载配置
        self.config = load_config()
        db_config = self.config.get("mysql", {})
        if not db_config:
            raise ValueError("❌ config.json 中未配置 mysql 数据库信息")

        # 连接数据库
        self.db = MySQLDB(**db_config)
        self.db.connect()

        # 注册SQL函数
        from plugins.conversation_history.sql import CONVERSATION_HISTORY_SQL_FUNCTIONS
        self.db.register_plugin_functions("ConversationConfigManager", CONVERSATION_HISTORY_SQL_FUNCTIONS)

        # 初始化表
        try:
            self.db.init_conversation_tables()
            print("✅ 对话记录表初始化完成")
        except Exception as e:
            print(f"❌ 初始化对话记录表失败: {e}")

    def list_configs(self):
        """列出所有对话配置"""
        cursor = self.db.connection.cursor(dictionary=True)
        cursor.execute("""
            SELECT chat_name, chat_type, enabled, context_limit,
                   session_hours, ai_agent_id, trigger_keywords,
                   created_at, updated_at
            FROM conversation_config
            ORDER BY chat_name, chat_type
        """)

        configs = cursor.fetchall()

        if not configs:
            print("📝 暂无对话配置")
            return

        print("📋 对话配置列表:")
        print("-" * 100)
        print(f"{'聊天名称':<20} {'类型':<8} {'启用':<6} {'上下文':<8} {'会话时长':<10} {'AI智能体':<10} {'更新时间':<20}")
        print("-" * 100)

        for config in configs:
            enabled = "✅" if config['enabled'] else "❌"
            ai_agent = config['ai_agent_id'] or "未配置"
            updated_at = config['updated_at'].strftime('%Y-%m-%d %H:%M')

            print(f"{config['chat_name']:<20} {config['chat_type']:<8} {enabled:<6} "
                  f"{config['context_limit']:<8} {config['session_hours']}小时{'':<4} "
                  f"{ai_agent:<10} {updated_at:<20}")

    def add_config(self, chat_name: str, chat_type: str = 'group',
                   enabled: bool = True, context_limit: int = 100,
                   session_hours: int = 24, ai_agent_id: int = None,
                   trigger_keywords: list = None):
        """添加或更新对话配置"""

        if trigger_keywords is None:
            trigger_keywords = ["AI", "ai", "助手", "机器人"]

        try:
            self.db.upsert_conversation_config(
                chat_name=chat_name,
                chat_type=chat_type,
                enabled=enabled,
                context_limit=context_limit,
                session_hours=session_hours,
                ai_agent_id=ai_agent_id,
                trigger_keywords=json.dumps(trigger_keywords, ensure_ascii=False)
            )
            print(f"✅ 成功配置聊天 '{chat_name}' ({chat_type})")
        except Exception as e:
            print(f"❌ 配置失败: {e}")

    def remove_config(self, chat_name: str, chat_type: str = 'group'):
        """删除对话配置"""
        cursor = self.db.connection.cursor()
        cursor.execute("""
            DELETE FROM conversation_config
            WHERE chat_name = %s AND chat_type = %s
        """, (chat_name, chat_type))

        if cursor.rowcount > 0:
            self.db.connection.commit()
            print(f"✅ 成功删除聊天 '{chat_name}' ({chat_type}) 的配置")
        else:
            print(f"⚠️ 未找到聊天 '{chat_name}' ({chat_type}) 的配置")

    def list_ai_agents(self):
        """列出可用的AI智能体"""
        cursor = self.db.connection.cursor(dictionary=True)
        cursor.execute("""
            SELECT id, name, description, model_type, created_at
            FROM ai_agent_profiles
            ORDER BY id
        """)

        agents = cursor.fetchall()

        if not agents:
            print("🤖 暂无AI智能体配置")
            return

        print("🤖 可用AI智能体:")
        print("-" * 80)
        print(f"{'ID':<5} {'名称':<20} {'模型类型':<15} {'描述':<30}")
        print("-" * 80)

        for agent in agents:
            description = (agent['description'] or '')[:28] + '...' if len(agent['description'] or '') > 30 else (agent['description'] or '')
            print(f"{agent['id']:<5} {agent['name']:<20} {agent['model_type']:<15} {description:<30}")

    def view_conversation_history(self, chat_name: str, chat_type: str = 'group', limit: int = 20, sender: str = None):
        """查看对话历史"""
        if sender:
            # 查看特定用户的对话历史
            context_messages = self.db.get_user_conversation_context(chat_name, chat_type, sender, limit)
            title = f"💬 聊天 '{chat_name}' ({chat_type}) 中用户 '{sender}' 的对话记录"
        else:
            # 查看所有对话历史
            context_messages = self.db.get_conversation_context(chat_name, chat_type, limit)
            title = f"💬 聊天 '{chat_name}' ({chat_type}) 的对话记录"

        if not context_messages:
            print(f"📝 {title} - 暂无记录")
            return

        print(f"{title} (最近{len(context_messages)}条):")
        print("-" * 80)

        for msg in context_messages:
            role = "🤖" if msg['message_type'] == 'bot' else "👤"
            sender_name = msg.get('sender', '未知')
            time_str = msg['created_at'].strftime('%H:%M:%S')
            content = msg['message_content'][:60] + '...' if len(msg['message_content']) > 60 else msg['message_content']

            print(f"{role} {time_str} {sender_name}: {content}")

    def view_user_list(self, chat_name: str, chat_type: str = 'group'):
        """查看聊天中的用户列表"""
        cursor = self.db.connection.cursor(dictionary=True)
        cursor.execute("""
            SELECT sender, COUNT(*) as message_count, MAX(created_at) as last_message_time
            FROM conversation_history
            WHERE chat_name = %s AND chat_type = %s AND message_type = 'user'
            GROUP BY sender
            ORDER BY last_message_time DESC
        """, (chat_name, chat_type))

        users = cursor.fetchall()

        if not users:
            print(f"📝 聊天 '{chat_name}' ({chat_type}) 暂无用户记录")
            return

        print(f"👥 聊天 '{chat_name}' ({chat_type}) 的用户列表:")
        print("-" * 60)
        print(f"{'用户名':<20} {'消息数':<10} {'最后发言时间':<20}")
        print("-" * 60)

        for user in users:
            last_time = user['last_message_time'].strftime('%Y-%m-%d %H:%M')
            print(f"{user['sender']:<20} {user['message_count']:<10} {last_time:<20}")

    def cleanup_old_data(self, days: int = 30):
        """清理旧数据"""
        try:
            deleted_count = self.db.cleanup_old_conversations(days)
            print(f"✅ 成功清理 {deleted_count} 条超过 {days} 天的对话记录")
        except Exception as e:
            print(f"❌ 清理失败: {e}")


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("""
对话记录配置管理工具

使用方法:
  python conversation_config_manager.py list                    # 列出所有配置
  python conversation_config_manager.py agents                  # 列出AI智能体
  python conversation_config_manager.py add <聊天名称> [选项]     # 添加配置
  python conversation_config_manager.py remove <聊天名称>        # 删除配置
  python conversation_config_manager.py history <聊天名称>       # 查看对话历史
  python conversation_config_manager.py users <聊天名称>         # 查看用户列表
  python conversation_config_manager.py user-history <聊天名称> <用户名>  # 查看特定用户的对话历史
  python conversation_config_manager.py cleanup [天数]          # 清理旧数据

示例:
  python conversation_config_manager.py add "测试群" --type group --agent-id 1
  python conversation_config_manager.py history "测试群" --limit 50
  python conversation_config_manager.py users "测试群"
  python conversation_config_manager.py user-history "测试群" "张三"
        """)
        return

    try:
        manager = ConversationConfigManager()
        command = sys.argv[1]

        if command == 'list':
            manager.list_configs()
        elif command == 'agents':
            manager.list_ai_agents()
        elif command == 'add' and len(sys.argv) >= 3:
            chat_name = sys.argv[2]
            # 这里可以添加更多参数解析
            manager.add_config(chat_name)
        elif command == 'remove' and len(sys.argv) >= 3:
            chat_name = sys.argv[2]
            manager.remove_config(chat_name)
        elif command == 'history' and len(sys.argv) >= 3:
            chat_name = sys.argv[2]
            manager.view_conversation_history(chat_name)
        elif command == 'users' and len(sys.argv) >= 3:
            chat_name = sys.argv[2]
            manager.view_user_list(chat_name)
        elif command == 'user-history' and len(sys.argv) >= 4:
            chat_name = sys.argv[2]
            sender = sys.argv[3]
            manager.view_conversation_history(chat_name, sender=sender)
        elif command == 'cleanup':
            days = int(sys.argv[2]) if len(sys.argv) >= 3 else 30
            manager.cleanup_old_data(days)
        else:
            print("❌ 无效的命令或参数")

    except Exception as e:
        print(f"❌ 执行失败: {e}")


if __name__ == '__main__':
    main()
