from typing import List, Optional
from . import Plugin

class OldBlacklistPlugin(Plugin):
    """
    旧的硬编码黑名单插件：屏蔽特定群聊或好友的消息处理
    已废弃，请使用 blacklist_plugin 目录下的数据库版本
    """

    priority = 999  # 设置为最低优先级，避免与新版本冲突

    def __init__(self, handler):
        super().__init__(handler)
        self.blacklist = {
            "Elik",
            "ddd",
            "守望大佬爱学习",
            "xianyu项目",
        }
        # 输出警告信息
        handler._log("[警告] 正在使用已废弃的硬编码黑名单插件，请使用数据库版本的BlacklistPlugin", 'WARNING')

    def on_messages(self, chat: str, messages: List[str]) -> Optional[str]:
        # 这个插件已被废弃，不再处理消息
        return None