"""程序入口：根据 config.json 选择批量或单条回复模式"""

import os
import json
from auto_hander import WeChatMessageHandler  # 批量静默聚合版
from single import SingleMessageHandler   # 单条即时回复版
from utils.config_utils import load_config

CONFIG_FILE = os.path.join(os.path.dirname(__file__), 'config.json')


def main():
    cfg = load_config()
    mode = (cfg.get('mode') or 'batch').lower()
    if mode in ('single', 'one', 'item'):
        SingleMessageHandler().run()
    else:
        WeChatMessageHandler().run()


if __name__ == '__main__':
    main()
