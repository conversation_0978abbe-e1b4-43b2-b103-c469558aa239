# 用户数据采集优化说明

## 概述

本次优化为微信用户数据采集插件增加了智能化的定时管理功能，解决了每次程序启动都重新获取用户数据的问题。

## 主要改进

### 1. 智能采集判断
- **时间间隔控制**: 根据设定的时间间隔判断是否需要重新采集数据
- **配置驱动**: 支持用户自定义采集策略
- **状态记录**: 记录程序启动/停止时间，计算运行时长

### 2. 新增数据库表

#### program_status_log (程序状态日志表)
```sql
CREATE TABLE program_status_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    status ENUM('startup', 'shutdown') NOT NULL,
    timestamp DATETIME NOT NULL,
    INDEX idx_timestamp (timestamp)
);
```

#### user_data_config (用户数据配置表)
```sql
CREATE TABLE user_data_config (
    id INT PRIMARY KEY DEFAULT 1,
    force_collection_on_startup BOOLEAN DEFAULT FALSE,
    collection_interval_hours INT DEFAULT 24,
    last_collection_time DATETIME NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 3. 配置选项

在 `config.json` 中新增了 `user_data_collection` 配置段：

```json
{
  "user_data_collection": {
    "force_collection_on_startup": false,
    "collection_interval_hours": 24,
    "description": "用户数据采集配置说明"
  }
}
```

**配置说明:**
- `force_collection_on_startup`: 是否强制在每次启动时采集数据
- `collection_interval_hours`: 数据采集间隔（小时）

## 使用方法

### 1. 自动运行
程序启动时会自动：
1. 记录启动时间
2. 检查上次采集时间
3. 根据配置和时间间隔智能判断是否需要采集
4. 启动后台定时任务

### 2. 配置管理工具
运行配置管理工具：
```bash
python user_data_config_manager.py
```

**功能菜单:**
- 查看当前配置
- 设置强制启动采集 (开启/关闭)
- 设置采集间隔 (小时)
- 重置最后采集时间
- 查看程序状态日志

### 3. 编程接口
在代码中可以通过插件实例调用：

```python
# 获取用户信息插件实例
user_info_plugin = None
for plugin in handler.plugins:
    if isinstance(plugin, WeChatUserInfoPlugin):
        user_info_plugin = plugin
        break

if user_info_plugin:
    # 设置配置
    user_info_plugin.set_collection_config(
        force_on_startup=True,  # 强制启动采集
        interval_hours=12       # 12小时间隔
    )
    
    # 获取状态
    status = user_info_plugin.get_collection_status()
    print(status)
```

## 工作流程

### 程序启动时
1. **记录启动状态**: 在 `program_status_log` 表中记录启动时间
2. **读取配置**: 从数据库读取用户配置
3. **智能判断**: 
   - 如果 `force_collection_on_startup=True`，直接采集
   - 如果是首次运行（无采集记录），执行采集
   - 如果距离上次采集时间超过设定间隔，执行采集
   - 否则跳过采集
4. **启动定时任务**: 根据配置的间隔启动后台定时任务

### 程序运行中
- 后台定时任务按设定间隔检查是否需要采集
- 每次采集后更新 `last_collection_time`

### 程序退出时
- 记录停止状态到 `program_status_log` 表

## 日志示例

```
[微信用户信息采集插件] [INFO] ✅ 用户数据相关表初始化完成
[微信用户信息采集插件] [INFO] ✅ 程序启动状态已记录
[微信用户信息采集插件] [INFO] ⏳ 距离上次采集仅过 2:30:15，还需等待 21:29:45 才会采集
[微信用户信息采集插件] [INFO] ⏭️ 根据配置和时间间隔，跳过本次数据采集
[微信用户信息采集插件] [INFO] ✅ 已启动智能定时采集任务
[微信用户信息采集插件] [INFO] ⏳ 下次智能采集将在 2024-01-15 14:30:00 执行，等待 24 小时
```

## 优势

1. **避免重复采集**: 不再每次启动都采集数据，减少对微信客户端的压力
2. **灵活配置**: 用户可以根据需要调整采集策略
3. **状态追踪**: 完整记录程序运行状态和采集历史
4. **智能恢复**: 程序异常退出后重启能正确计算时间间隔
5. **易于管理**: 提供图形化配置工具，方便用户操作

## 注意事项

1. 首次运行时会自动创建相关数据库表
2. 配置更改后立即生效，无需重启程序
3. 如需强制重新采集，可以通过配置工具重置最后采集时间
4. 程序异常退出时可能无法记录停止状态，但不影响下次启动的时间计算
