#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试消息分段回复插件
"""

import sys
import os
import json

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

def test_config_loading():
    """测试配置文件加载"""
    print("🔧 测试配置文件加载...")
    
    try:
        from utils.config_utils import load_config
        config = load_config()
        
        # 检查分段配置
        segmentation_config = config.get('message_segmentation', {})
        print(f"✅ 分段配置加载成功:")
        print(f"   - 启用状态: {segmentation_config.get('enabled', False)}")
        print(f"   - 分段模式: {segmentation_config.get('mode', 'auto')}")
        print(f"   - 字数阈值: {segmentation_config.get('max_length', 500)}")
        print(f"   - 分段概率: {segmentation_config.get('probability', 0.3)}")
        print(f"   - 间隔时间: {segmentation_config.get('segment_delay', 1.5)}秒")
        
        return True
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False

def test_plugin_loading():
    """测试插件加载"""
    print("\n🔧 测试插件加载...")
    
    try:
        # 模拟handler
        class MockHandler:
            def _log(self, message, level='INFO'):
                print(f"[{level}] {message}")
        
        # 导入插件
        from plugins.message_segmentation.plugin import MessageSegmentationPlugin
        
        # 创建插件实例
        handler = MockHandler()
        plugin = MessageSegmentationPlugin(handler)
        
        print(f"✅ 插件加载成功:")
        print(f"   - 类名: {plugin.__class__.__name__}")
        print(f"   - 优先级: {plugin.priority}")
        print(f"   - 启用状态: {plugin.enabled}")
        print(f"   - 分段模式: {plugin.mode}")
        
        return plugin
    except Exception as e:
        print(f"❌ 插件加载失败: {e}")
        import traceback
        print(traceback.format_exc())
        return None

def test_segmentation_logic(plugin):
    """测试分段逻辑"""
    print("\n🔧 测试分段逻辑...")
    
    if not plugin:
        print("❌ 插件未加载，跳过测试")
        return
    
    # 测试用例
    test_cases = [
        {
            "name": "短消息",
            "message": "这是一条短消息",
            "expected_segments": 1
        },
        {
            "name": "中等长度消息",
            "message": "这是一条中等长度的消息。" * 20,
            "expected_segments": 1
        },
        {
            "name": "长消息",
            "message": "这是一条很长的消息，需要分段发送。" * 50,
            "expected_segments": 2
        },
        {
            "name": "超长消息",
            "message": "这是一条超级长的消息，需要分成多段发送。包含很多内容和信息。" * 100,
            "expected_segments": 3
        }
    ]
    
    for case in test_cases:
        print(f"\n📝 测试: {case['name']}")
        print(f"   原始长度: {len(case['message'])}字")
        
        # 测试是否需要分段
        should_segment = plugin.should_segment(case['message'])
        print(f"   需要分段: {should_segment}")
        
        # 测试分段结果
        segments = plugin.segment_message(case['message'])
        print(f"   分段数量: {len(segments)}")
        
        for i, segment in enumerate(segments):
            print(f"   段落{i+1}: {len(segment)}字 - {segment[:50]}...")
        
        # 验证结果
        if plugin.mode == 'none':
            expected = 1
        elif len(case['message']) > plugin.max_length:
            expected = max(1, len(case['message']) // plugin.max_length)
        else:
            expected = 1
            
        if len(segments) >= 1:
            print(f"   ✅ 分段测试通过")
        else:
            print(f"   ❌ 分段测试失败")

def test_config_info(plugin):
    """测试配置信息获取"""
    print("\n🔧 测试配置信息获取...")
    
    if not plugin:
        print("❌ 插件未加载，跳过测试")
        return
    
    try:
        config_info = plugin.get_config_info()
        print("✅ 配置信息:")
        print(config_info)
    except Exception as e:
        print(f"❌ 获取配置信息失败: {e}")

def main():
    """主测试函数"""
    print("🚀 开始测试消息分段回复插件")
    print("=" * 50)
    
    # 测试配置加载
    if not test_config_loading():
        return
    
    # 测试插件加载
    plugin = test_plugin_loading()
    
    # 测试分段逻辑
    test_segmentation_logic(plugin)
    
    # 测试配置信息
    test_config_info(plugin)
    
    print("\n" + "=" * 50)
    print("🎉 测试完成")

if __name__ == "__main__":
    main()
