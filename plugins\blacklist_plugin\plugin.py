from typing import List, Optional, Dict, Any
from core.plugin_base import Plugin
from .sql import BLACKLIST_SQL_FUNCTIONS


class BlacklistPlugin(Plugin):
    """
    黑名单插件：屏蔽特定群聊或好友的消息处理
    默认优先级：0（最高优先级）
    """

    priority = 0  # 最高优先级

    def __init__(self, handler):
        super().__init__(handler)

        # 使用主处理程序提供的数据库连接
        self.db = getattr(handler, 'db', None)
        if not self.db:
            raise ValueError("❌ handler 未绑定数据库实例，无法继续")

        # 注册本插件所需的 SQL 函数
        self.db.register_plugin_functions("BlacklistPlugin", BLACKLIST_SQL_FUNCTIONS)

        # 缓存黑名单数据
        self.cache = self._load_blacklist_from_db()

    def __del__(self):
        """插件析构时卸载SQL函数"""
        if hasattr(self, 'db') and self.db:
            self.db.unregister_plugin_functions("BlacklistPlugin")

    def _load_blacklist_from_db(self) -> Dict[str, Any]:
        """从数据库加载黑名单数据"""
        blacklist_data = self.db.get_all_blacklist_entries()

        cache = {
            "group_blacklist": set(),
            "private_blacklist": set(),
            "global_blacklist": set()
        }

        for entry in blacklist_data:
            chat_name = entry["chat_name"]
            chat_type = entry["chat_type"]
            is_global = entry["is_global"]

            if is_global:
                cache["global_blacklist"].add(chat_name)
            elif chat_type == "group":
                cache["group_blacklist"].add(chat_name)
            else:
                cache["private_blacklist"].add(chat_name)

        return cache

    def on_messages(self, chat: str, messages: List[str]) -> Optional[str]:
        # 检查全局黑名单
        if chat in self.cache["global_blacklist"]:
            self.handler._log(f"[黑名单] 跳过处理聊天窗口: {chat} (全局黑名单)")
            return "__BLACKLISTED__"

        # 检查具体黑名单
        if (chat in self.cache["group_blacklist"] or
                chat in self.cache["private_blacklist"]):
            self.handler._log(f"[黑名单] 跳过处理聊天窗口: {chat}")
            return "__BLACKLISTED__"

        return None

    def add_to_blacklist(self, chat_name: str, chat_type: str = "group", is_global: bool = False) -> bool:
        """添加到黑名单"""
        success = self.db.add_blacklist_entry(chat_name, chat_type, is_global)
        if success:
            # 更新缓存
            if is_global:
                self.cache["global_blacklist"].add(chat_name)
            elif chat_type == "group":
                self.cache["group_blacklist"].add(chat_name)
            else:
                self.cache["private_blacklist"].add(chat_name)
        return success

    def remove_from_blacklist(self, chat_name: str) -> bool:
        """从黑名单移除"""
        success = self.db.remove_blacklist_entry(chat_name)
        if success:
            # 更新缓存
            self.cache["global_blacklist"].discard(chat_name)
            self.cache["group_blacklist"].discard(chat_name)
            self.cache["private_blacklist"].discard(chat_name)
        return success