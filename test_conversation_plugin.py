#!/usr/bin/env python3
"""
对话记录插件测试脚本
"""

import os
import sys
import json
from datetime import datetime
from utils.config_utils import load_config
from db_plugins.mysql import MySQLDB


def test_conversation_plugin():
    """测试对话记录插件功能"""
    print("🧪 开始测试对话记录插件...")
    
    try:
        # 加载配置
        cfg = load_config()
        db_config = cfg.get("mysql", {})
        if not db_config:
            print("❌ 未找到数据库配置")
            return False
        
        # 连接数据库
        db = MySQLDB(**db_config)
        db.connect()
        
        # 注册SQL函数
        from plugins.conversation_history.sql import CONVERSATION_HISTORY_SQL_FUNCTIONS
        db.register_plugin_functions("TestConversation", CONVERSATION_HISTORY_SQL_FUNCTIONS)
        
        # 测试初始化表
        print("📋 测试初始化数据库表...")
        db.init_conversation_tables()
        print("✅ 数据库表初始化成功")
        
        # 测试配置管理
        print("⚙️ 测试配置管理...")
        test_chat = "测试群聊"
        test_chat_type = "group"
        
        # 创建配置
        db.upsert_conversation_config(
            chat_name=test_chat,
            chat_type=test_chat_type,
            enabled=True,
            context_limit=50,
            session_hours=24,
            ai_agent_id=1,
            trigger_keywords=json.dumps(["测试", "AI"])
        )
        print("✅ 配置创建成功")
        
        # 读取配置
        config = db.get_conversation_config(test_chat, test_chat_type)
        if config:
            print(f"✅ 配置读取成功: {config['chat_name']}")
        else:
            print("❌ 配置读取失败")
            return False
        
        # 测试消息保存
        print("💬 测试消息保存...")
        test_messages = [
            "你好，这是第一条测试消息",
            "这是第二条测试消息",
            "AI 请帮我回答一个问题"
        ]
        
        for i, message in enumerate(test_messages):
            db.save_message(
                chat_name=test_chat,
                chat_type=test_chat_type,
                sender=f"测试用户{i+1}",
                message_content=message,
                message_type='user',
                session_hours=24
            )
        
        print(f"✅ 成功保存 {len(test_messages)} 条用户消息")
        
        # 保存一条AI回复
        db.save_message(
            chat_name=test_chat,
            chat_type=test_chat_type,
            sender="AI助手",
            message_content="这是AI的测试回复",
            message_type='bot',
            session_hours=24
        )
        print("✅ 成功保存AI回复")
        
        # 测试上下文查询
        print("📖 测试上下文查询...")
        context_messages = db.get_conversation_context(test_chat, test_chat_type, 10, 24)
        
        if context_messages:
            print(f"✅ 成功查询到 {len(context_messages)} 条上下文消息")
            for msg in context_messages:
                role = "🤖" if msg['message_type'] == 'bot' else "👤"
                print(f"  {role} {msg['sender']}: {msg['message_content'][:30]}...")
        else:
            print("❌ 上下文查询失败")
            return False
        
        # 测试会话ID生成
        print("🔑 测试会话ID生成...")
        session_id1 = db.generate_session_id(test_chat, test_chat_type, 24)
        session_id2 = db.generate_session_id(test_chat, test_chat_type, 24)
        
        if session_id1 == session_id2:
            print(f"✅ 会话ID生成一致: {session_id1}")
        else:
            print(f"❌ 会话ID生成不一致: {session_id1} vs {session_id2}")
            return False
        
        # 测试AI智能体查询
        print("🤖 测试AI智能体查询...")
        agent = db.get_ai_agent_by_id(1)
        if agent:
            print(f"✅ 成功查询AI智能体: {agent.get('name', '未知')}")
        else:
            print("⚠️ 未找到AI智能体 ID=1，这是正常的（如果数据库中没有配置）")
        
        # 测试数据清理
        print("🧹 测试数据清理...")
        deleted_count = db.cleanup_old_conversations(0)  # 清理所有数据
        print(f"✅ 清理了 {deleted_count} 条测试数据")
        
        print("🎉 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_plugin_integration():
    """测试插件集成"""
    print("\n🔌 测试插件集成...")
    
    try:
        # 模拟handler
        class MockHandler:
            def __init__(self):
                cfg = load_config()
                db_config = cfg.get("mysql", {})
                from db_plugins.mysql import MySQLDB
                self.db = MySQLDB(**db_config)
                self.db.connect()
                self.chat_type_cache = {"测试群聊": "group"}
            
            def _log(self, message, level="INFO"):
                print(f"[{level}] {message}")
        
        # 创建插件实例
        from plugins.conversation_history.plugin import ConversationHistoryPlugin
        handler = MockHandler()
        plugin = ConversationHistoryPlugin(handler)
        
        print("✅ 插件实例化成功")
        
        # 测试消息处理
        test_messages = ["AI 你好", "请帮我解答一个问题"]
        result = plugin.on_messages("测试群聊", test_messages)
        
        if result is None:
            print("✅ 插件处理消息成功（未配置AI智能体，返回None是正常的）")
        else:
            print(f"✅ 插件生成回复: {result[:50]}...")
        
        print("🎉 插件集成测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 插件集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("=" * 60)
    print("对话记录插件测试")
    print("=" * 60)
    
    # 测试基础功能
    if not test_conversation_plugin():
        print("❌ 基础功能测试失败")
        return
    
    # 测试插件集成
    if not test_plugin_integration():
        print("❌ 插件集成测试失败")
        return
    
    print("\n" + "=" * 60)
    print("🎉 所有测试完成！")
    print("=" * 60)
    
    print("\n📝 下一步操作:")
    print("1. 使用 conversation_config_manager.py 配置聊天窗口")
    print("2. 在数据库中配置AI智能体")
    print("3. 重启微信机器人以加载新插件")
    print("\n示例命令:")
    print("  python conversation_config_manager.py list")
    print("  python conversation_config_manager.py agents")
    print("  python conversation_config_manager.py add '你的群聊名称'")


if __name__ == '__main__':
    main()
