# 微信机器人逻辑文档

## 概述
这是一个基于 wxautox 库的微信自动回复机器人，支持插件化架构，可以根据配置选择批量处理或单条即时回复模式。

## 程序入口与模式选择

### 主程序 (main.py)
程序从 `main.py` 开始执行，根据 `config.json` 中的 `mode` 配置选择运行模式：

- **批量模式 (batch)**：默认模式，使用 `WeChatMessageHandler` 类
- **单条模式 (single/one/item)**：即时回复模式，使用 `SingleMessageHandler` 类

```python
def main():
    cfg = load_config()
    mode = (cfg.get('mode') or 'batch').lower()
    if mode in ('single', 'one', 'item'):
        SingleMessageHandler().run()
    else:
        WeChatMessageHandler().run()
```

## 批量处理模式 (auto_hander.py)

### 核心机制
批量模式采用"静默聚合"策略，收集一段时间内的消息后统一处理：

#### 关键参数
- `QUIET_SEC = 6`：静默阈值（秒），消息停止6秒后开始处理
- `INACTIVE_MIN = 1`：监听超时（分钟），1分钟无消息则移除监听
- `DEBOUNCE_WINDOW = 3.0`：防抖窗口（秒），防止同一用户连续发送消息

#### 消息接收流程
1. **监听消息**：通过 `wx.GetListenMessage()` 和 `wx.GetNextNewMessage()` 获取消息
2. **消息缓存**：将消息存储到 `buffers` 字典中，按聊天窗口分组
3. **防抖处理**：同一发送者在3秒内的消息会被延迟处理
4. **静默检测**：当某个聊天窗口6秒内无新消息时，触发批量处理

#### 聊天类型识别
系统会自动识别聊天类型并缓存：
- **群聊 (group)**：多人聊天群组
- **私聊 (friend)**：一对一私人聊天

```python
chat_info = chat_wnd.ChatInfo()
chat_type = chat_info.get('chat_type', 'friend')
self.chat_type_cache[chat] = chat_type
```

## 单条处理模式 (single.py)

### 核心机制
单条模式继承自批量模式，但改写为"收到一条立刻回复一条"：

#### 关键特性
- `QUIET_SEC = 0`：不使用静默阈值，立即处理
- `MIN_INTERVAL_SEC = 1.0`：两条回复最小间隔，防止消息连发
- **去重机制**：避免重复回复相同内容
- **频率限制**：控制回复频率，防止刷屏

#### 处理流程
1. **直接处理**：覆盖 `_cache_msgs` 方法，不缓存直接处理
2. **逐条回复**：每收到一条消息立即调用插件处理
3. **去重检查**：检查是否与上次回复内容相同且间隔过短
4. **频率控制**：确保两次回复间隔不少于1秒

## 插件系统架构

### 插件发现与加载
系统会自动扫描 `plugins` 目录下的所有子目录，寻找继承自 `Plugin` 或 `StartupPlugin` 的类：

```python
def discover_plugins() -> List[Type[Union[Plugin, StartupPlugin]]]:
    # 扫描 plugins 目录
    # 动态导入模块
    # 查找插件类
    # 返回插件类列表
```

### 插件基类 (Plugin)
所有插件必须继承 `Plugin` 基类并实现 `on_messages` 方法：

```python
class Plugin(ABC):
    priority = 0  # 优先级，数字越小越先执行
    blacklist = []  # 黑名单聊天列表
    
    @abstractmethod
    def on_messages(self, chat: str, messages: List[str]) -> Optional[str]:
        # 处理消息并返回回复内容或None
        pass
```

### 插件执行顺序
插件按 `priority` 属性排序执行，数字越小优先级越高：
- **关键词回复插件**：priority = 10
- **AI聊天插件**：priority = 150

## 消息处理流程

### 群聊消息处理 (group_reply.py)

#### 处理逻辑
1. **关键词优先**：首先尝试所有插件的关键词匹配
2. **@机器人检测**：检查消息中是否包含 `@机器人名称`
3. **AI回复**：对@机器人的消息使用AI插件处理

#### @检测机制
```python
at_me = False
m = re.search(r"@([^@]+)", content)
if m:
    at_name = re.sub(r"[\s\u200B-\u200D\u202F\u2060\uFEFF]", "", m.group(1))
    if at_name == bot_name:
        at_me = True
```

#### 回复格式
- **关键词回复**：直接发送回复内容
- **AI回复**：如果启用@回复功能，会自动@提及的用户

```python
if enable_at_reply and unique_senders:
    at_part = ''.join([f"{{@{s}}}" for s in unique_senders])
    msg_text = f"{at_part}\n{reply}"
```

### 私聊消息处理 (private_reply.py)

#### 处理逻辑
1. **关键词优先**：首先尝试关键词插件匹配
2. **AI回复**：私聊中无需@机器人，直接使用AI插件处理

#### 发送方式
- 群聊使用 `SendTypingText`（打字机效果）
- 私聊使用 `SendMsg`（普通发送）

## 具体插件实现

### 关键词回复插件 (KeywordReplyPlugin)

#### 功能特性
- **数据库驱动**：从MySQL读取关键词规则
- **分类管理**：区分群聊和私聊规则
- **全局/局部规则**：支持全局默认规则和聊天特定规则
- **优先级**：priority = 10（高优先级）

#### 匹配逻辑
1. **局部规则优先**：先匹配聊天特定的关键词规则
2. **全局规则兜底**：如果启用全局规则，再匹配默认规则
3. **逆序匹配**：从最新消息开始匹配关键词

### AI聊天插件 (AIChatPlugin)

#### 功能特性
- **多模型支持**：支持GPT-3.5、Dify等多种AI模型
- **关键词触发**：通过关键词触发AI回复
- **上下文构建**：将多条消息组合为对话上下文
- **优先级**：priority = 150（低优先级）

#### 模型映射
```python
MODEL_MAP = {
    "gpt-3.5-turbo": GPT35TurboModel,
    "dify_chatflow": DifyChatFlowModel,
    "dify_workflow": DifyWorkFlowModel,
    "dify_agent": DifyAgentModel
}
```

## 数据库集成

### 插件化SQL管理
每个插件都有自己的SQL函数文件，实现了插件级别的数据库操作隔离：

```python
# 注册插件SQL函数
self.db.register_plugin_functions("PluginName", SQL_FUNCTIONS)

# 卸载插件SQL函数
self.db.unregister_plugin_functions("PluginName")
```

### 配置管理
系统通过 `config.json` 管理数据库连接和各种功能开关：

```json
{
  "mode": "batch",
  "bot_name": "春",
  "mysql": {
    "host": "localhost",
    "user": "root", 
    "password": "nmnm1239",
    "database": "wechat_bot"
  },
  "enable_at_reply": true,
  "enable_private_ai": true
}
```

## 总结

这个微信机器人采用了高度模块化的设计：

1. **双模式支持**：批量聚合和即时回复两种处理模式
2. **插件化架构**：通过插件系统实现功能扩展
3. **智能消息处理**：区分群聊和私聊，支持@检测
4. **数据库驱动**：配置和规则存储在MySQL中
5. **防抖和频控**：避免消息重复和刷屏问题

整个系统的核心思想是：**接收消息 → 插件处理 → 智能回复**，通过插件的优先级和黑名单机制实现灵活的消息处理策略。
