#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终的分段效果（无续标识，1秒间隔）
"""

import sys
import os
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

class MockWeChat:
    """模拟微信发送接口"""
    
    def __init__(self):
        self.sent_messages = []
        self.message_count = 0
    
    def SendMsg(self, message, who=None):
        """模拟发送消息"""
        self.message_count += 1
        timestamp = time.strftime("%H:%M:%S")
        self.sent_messages.append({
            'id': self.message_count,
            'timestamp': timestamp,
            'chat': who,
            'message': message,
            'method': 'SendMsg'
        })
        print(f"📤 [{timestamp}] 第{self.message_count}条消息发送到 '{who}':")
        print(f"    {message}")
        print()
    
    def SendTypingText(self, message, who=None):
        """模拟发送打字机消息"""
        self.message_count += 1
        timestamp = time.strftime("%H:%M:%S")
        self.sent_messages.append({
            'id': self.message_count,
            'timestamp': timestamp,
            'chat': who,
            'message': message,
            'method': 'SendTypingText'
        })
        print(f"📤 [{timestamp}] 第{self.message_count}条消息发送到 '{who}' (打字机):")
        print(f"    {message}")
        print()

def test_final_segmentation():
    """测试最终分段效果"""
    print("🚀 测试最终分段效果（无续标识，1秒间隔）")
    print("=" * 70)
    
    # 创建模拟handler
    class MockHandler:
        def _log(self, message, level='INFO'):
            print(f"[{level}] {message}")
    
    # 创建模拟微信实例
    mock_wx = MockWeChat()
    
    # 导入插件
    from plugins.message_segmentation.plugin import MessageSegmentationPlugin
    
    # 创建插件实例
    handler = MockHandler()
    plugin = MessageSegmentationPlugin(handler)
    
    print(f"📋 当前配置:")
    print(f"   - 启用状态: {plugin.enabled}")
    print(f"   - 分段模式: {plugin.mode}")
    print(f"   - 字数阈值: {plugin.max_length}")
    print(f"   - 间隔时间: {plugin.segment_delay}秒")
    
    # 测试消息
    test_message = '''戏精附体版：  
"辣的！宝子这要求我太懂了（搓手手）海底捞新出的麻辣锅底配酥肉，辣到头皮发麻那种～咱直接安排上啊！🔥"

网络热梗版：  
"姐妹尊嘟要吃辣？建议狠狠点外卖来份变态辣炸鸡🍗绝绝子级别的辣味，CPU我都要燃烧起来了哈哈哈～"

温和婉转版：  
"想吃辣的啦？那咱们煮个简单的辣酱拌面吧～加点葱花和辣椒油，暖胃又过瘾（轻声）要不要试试看呢？"'''
    
    print(f"\n📝 测试消息:")
    print(f"   长度: {len(test_message)}字")
    
    # 获取分段结果
    segments = plugin.segment_message(test_message)
    print(f"\n📄 分段预览:")
    print(f"   分段数量: {len(segments)}")
    
    for i, segment in enumerate(segments, 1):
        print(f"\n   第{i}段 ({len(segment)}字):")
        print(f"   {segment}")
    
    print(f"\n🚀 开始模拟发送...")
    print("=" * 70)
    
    # 记录开始时间
    start_time = time.time()
    
    # 模拟发送
    plugin.send_segmented_message(
        wx=mock_wx,
        chat="守望大佬爱学习",
        message=test_message,
        at_users=["春面不觉晓"]
    )
    
    # 记录结束时间
    end_time = time.time()
    total_time = end_time - start_time
    
    print("=" * 70)
    print(f"📊 发送统计:")
    print(f"   - 发送消息数: {mock_wx.message_count}条")
    print(f"   - 总耗时: {total_time:.1f}秒")
    print(f"   - 平均间隔: {total_time/(max(1, mock_wx.message_count-1)):.1f}秒")
    
    print(f"\n✨ 用户看到的效果:")
    print("=" * 70)
    
    for i, msg in enumerate(mock_wx.sent_messages, 1):
        print(f"第{i}条消息 [{msg['timestamp']}]:")
        print(msg['message'])
        if i < len(mock_wx.sent_messages):
            print("⏱️  [等待1秒...]")
        print()
    
    print("🎉 测试完成！")
    print("✅ 无'(续)'标识")
    print("✅ 1秒间隔")
    print("✅ 多条独立消息")

if __name__ == "__main__":
    test_final_segmentation()
