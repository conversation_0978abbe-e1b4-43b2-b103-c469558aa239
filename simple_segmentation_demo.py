#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的分段效果演示
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

def main():
    """演示分段效果"""
    print("🚀 消息分段回复功能演示")
    print("=" * 50)
    
    # 创建模拟handler
    class MockHandler:
        def _log(self, message, level='INFO'):
            print(f"[{level}] {message}")
    
    # 导入插件
    from plugins.message_segmentation.plugin import MessageSegmentationPlugin
    
    # 创建插件实例
    handler = MockHandler()
    plugin = MessageSegmentationPlugin(handler)
    
    print(f"📋 当前配置:")
    print(f"   - 启用状态: {plugin.enabled}")
    print(f"   - 分段模式: {plugin.mode}")
    print(f"   - 字数阈值: {plugin.max_length}")
    print(f"   - 分段概率: {plugin.probability}")
    print(f"   - 间隔时间: {plugin.segment_delay}秒")
    
    # 测试不同长度的消息
    test_cases = [
        {
            "name": "短消息",
            "message": "这是一条短消息，不需要分段。"
        },
        {
            "name": "长消息",
            "message": (
                "这是一条很长的消息，用来测试分段功能。" * 20 +
                "消息内容包含了大量的信息和详细说明。" * 15 +
                "希望通过分段发送来提高用户的阅读体验。" * 10
            )
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{'='*50}")
        print(f"📝 测试 {i}: {case['name']}")
        print(f"{'='*50}")
        
        message = case['message']
        print(f"原始消息长度: {len(message)}字")
        print(f"消息预览: {message[:100]}...")
        
        # 检查是否需要分段
        should_segment = plugin.should_segment(message)
        print(f"\n需要分段: {should_segment}")
        
        # 获取分段结果
        segments = plugin.segment_message(message)
        print(f"分段数量: {len(segments)}")
        
        # 显示每个段落
        for j, segment in enumerate(segments, 1):
            print(f"\n📄 段落 {j} ({len(segment)}字):")
            print(f"   {segment}")
            
            if j < len(segments):
                print(f"   [间隔 {plugin.segment_delay} 秒]")
    
    print(f"\n{'='*50}")
    print("🎉 演示完成！")
    print("您可以修改 config.json 中的配置来调整分段行为")
    print(f"{'='*50}")

if __name__ == "__main__":
    main()
