# 定时消息插件使用说明

## 功能概述

定时消息插件支持向指定的群聊和私聊用户定时发送固定消息，支持多种调度类型：

- **每日定时**：每天固定时间发送
- **每周定时**：每周固定星期几的固定时间发送
- **每月定时**：每月固定日期的固定时间发送
- **间隔定时**：按固定时间间隔发送
- **一次性定时**：在指定时间发送一次

## 插件特性

- ✅ 支持群聊和私聊
- ✅ 多种调度类型
- ✅ 发送次数限制
- ✅ 任务启用/禁用
- ✅ 发送历史记录
- ✅ 自动故障恢复
- ✅ 数据库持久化存储

## 安装和配置

### 1. 插件自动加载

插件会在系统启动时自动加载，无需额外配置。

### 2. 数据库表自动创建

插件首次运行时会自动创建以下数据库表：
- `scheduled_messages`：定时消息任务表
- `scheduled_message_history`：发送历史记录表

## 使用方法

### 1. 使用管理工具

系统提供了 `scheduled_message_manager.py` 管理工具，可以通过命令行管理定时消息任务。

#### 查看所有任务
```bash
python scheduled_message_manager.py list
```

#### 添加每日定时消息
```bash
python scheduled_message_manager.py add_daily "早安问候" "测试群" group "早上好！新的一天开始了！" "08:30"
```

#### 添加每周定时消息
```bash
python scheduled_message_manager.py add_weekly "周报提醒" "工作群" group "请大家提交周报" 0 "17:00"
```
注：星期几参数：0=周一, 1=周二, ..., 6=周日

#### 添加间隔定时消息
```bash
python scheduled_message_manager.py add_interval "心跳检测" "监控群" group "系统正常运行中" 60
```
注：60表示每60分钟发送一次

#### 添加一次性定时消息
```bash
python scheduled_message_manager.py add_once "会议提醒" "项目群" group "会议将在30分钟后开始" "2024-12-25 14:30"
```

#### 删除任务
```bash
python scheduled_message_manager.py delete "任务名称"
```

#### 启用/禁用任务
```bash
python scheduled_message_manager.py enable "任务名称"
python scheduled_message_manager.py disable "任务名称"
```

#### 查看发送历史
```bash
python scheduled_message_manager.py history           # 查看所有任务历史
python scheduled_message_manager.py history "任务名称"  # 查看指定任务历史
```

### 2. 编程接口

也可以通过代码直接调用插件接口：

```python
# 获取插件实例
scheduled_plugin = None
for plugin in handler.plugins:
    if plugin.__class__.__name__ == 'ScheduledMessagePlugin':
        scheduled_plugin = plugin
        break

if scheduled_plugin:
    # 添加每日定时消息
    scheduled_plugin.add_daily_message(
        task_name="早安问候",
        chat_name="测试群",
        chat_type="group",
        message_content="早上好！新的一天开始了！",
        send_time="08:30"
    )
    
    # 添加每周定时消息
    scheduled_plugin.add_weekly_message(
        task_name="周报提醒",
        chat_name="工作群", 
        chat_type="group",
        message_content="请大家提交周报",
        weekday=0,  # 周一
        send_time="17:00"
    )
    
    # 添加间隔定时消息
    scheduled_plugin.add_interval_message(
        task_name="心跳检测",
        chat_name="监控群",
        chat_type="group", 
        message_content="系统正常运行中",
        interval_minutes=60
    )
```

## 参数说明

### 聊天类型 (chat_type)
- `group`：群聊
- `private`：私聊

### 调度类型 (schedule_type)
- `daily`：每日定时
- `weekly`：每周定时
- `monthly`：每月定时
- `interval`：间隔定时
- `once`：一次性定时

### 时间格式
- 时间：`HH:MM` (如 "08:30", "17:00")
- 日期时间：`YYYY-MM-DD HH:MM` (如 "2024-12-25 14:30")

## 注意事项

1. **任务名称唯一**：每个任务必须有唯一的名称
2. **聊天窗口名称**：必须与微信中的实际聊天窗口名称完全一致
3. **时间精度**：调度器每30秒检查一次，实际发送时间可能有30秒内的误差
4. **发送限制**：可以设置最大发送次数，防止无限发送
5. **错误处理**：发送失败会记录错误信息，不会影响其他任务

## 监控和维护

### 查看插件状态
插件启动时会在日志中显示：
```
✅ 定时消息插件已启动
🕐 定时消息调度器已启动
```

### 查看发送日志
每次发送消息时会记录日志：
```
📤 向群聊 '测试群' 发送定时消息: 早安问候
✅ 定时消息发送成功: 早安问候 -> 测试群
```

### 数据库维护
- 发送历史会持续累积，建议定期清理旧记录
- 可以通过数据库直接查询和管理任务

## 故障排除

### 常见问题

1. **消息发送失败**
   - 检查聊天窗口名称是否正确
   - 确认微信客户端正常运行
   - 查看错误日志获取详细信息

2. **任务不执行**
   - 确认任务已启用 (`enabled = TRUE`)
   - 检查下次发送时间是否正确
   - 查看插件是否正常启动

3. **时间不准确**
   - 调度器每30秒检查一次，存在延迟是正常的
   - 可以调整插件中的 `check_interval` 参数

### 调试方法

1. 查看数据库表内容：
```sql
SELECT * FROM scheduled_messages;
SELECT * FROM scheduled_message_history ORDER BY sent_at DESC LIMIT 10;
```

2. 检查插件日志输出

3. 使用管理工具查看任务状态和历史

## 扩展功能

插件设计为可扩展的，可以根据需要添加：
- 更复杂的调度规则
- 消息模板和变量替换
- 条件触发（如天气、股价等）
- 批量导入/导出任务
- Web管理界面
